<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录 - 聊天平台管理</title>
    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🔐</text></svg>">
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .login-card {
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.95);
        }
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 12px 24px;
            border-radius: 8px;
            color: white;
            font-weight: 500;
            z-index: 1000;
            transform: translateX(400px);
            transition: transform 0.3s ease-in-out;
        }
        .notification.show {
            transform: translateX(0);
        }
        .notification.success {
            background-color: #10b981;
        }
        .notification.error {
            background-color: #ef4444;
        }
        .notification.info {
            background-color: #3b82f6;
        }
    </style>
</head>
<body class="flex items-center justify-center min-h-screen">
    <!-- 通知消息 -->
    <div id="notification" class="notification"></div>

    <div class="login-card rounded-2xl shadow-2xl p-8 w-full max-w-md mx-4">
        <!-- Logo和标题 -->
        <div class="text-center mb-8">
            <div class="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full mb-4">
                <i class="fas fa-shield-alt text-2xl text-white"></i>
            </div>
            <h1 class="text-2xl font-bold text-gray-900 mb-2">管理员登录</h1>
            <p class="text-gray-600">请输入您的管理员凭据</p>
        </div>

        <!-- 登录表单 -->
        <form id="login-form" class="space-y-6">
            <div>
                <label for="username" class="block text-sm font-medium text-gray-700 mb-2">
                    <i class="fas fa-user mr-2"></i>用户名
                </label>
                <input
                    type="text"
                    id="username"
                    name="username"
                    required
                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition duration-200"
                    placeholder="请输入用户名"
                    autocomplete="username"
                >
            </div>

            <div>
                <label for="password" class="block text-sm font-medium text-gray-700 mb-2">
                    <i class="fas fa-lock mr-2"></i>密码
                </label>
                <div class="relative">
                    <input
                        type="password"
                        id="password"
                        name="password"
                        required
                        class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition duration-200 pr-12"
                        placeholder="请输入密码"
                        autocomplete="current-password"
                    >
                    <button
                        type="button"
                        id="toggle-password"
                        class="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600"
                    >
                        <i class="fas fa-eye" id="password-icon"></i>
                    </button>
                </div>
            </div>

            <div class="flex items-center justify-between">
                <label class="flex items-center">
                    <input type="checkbox" id="remember-me" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                    <span class="ml-2 text-sm text-gray-600">记住我</span>
                </label>
            </div>

            <button
                type="submit"
                id="login-btn"
                class="w-full bg-gradient-to-r from-blue-500 to-purple-600 text-white py-3 px-4 rounded-lg font-medium hover:from-blue-600 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition duration-200 transform hover:scale-105"
            >
                <i class="fas fa-sign-in-alt mr-2"></i>
                <span id="login-text">登录</span>
                <i class="fas fa-spinner fa-spin ml-2 hidden" id="login-spinner"></i>
            </button>
        </form>

        <!-- 返回链接 -->
        <div class="mt-6 text-center">
            <a href="/" class="text-sm text-gray-600 hover:text-gray-900 transition duration-200">
                <i class="fas fa-arrow-left mr-1"></i>返回首页
            </a>
        </div>

        <!-- 版本信息 -->
        <div class="mt-8 text-center text-xs text-gray-500">
            聊天平台管理系统 v1.0.0
        </div>
    </div>

    <script>
        class LoginApp {
            constructor() {
                this.init();
            }

            init() {
                this.bindEvents();
                this.checkExistingAuth();
            }

            bindEvents() {
                // 表单提交
                document.getElementById('login-form').addEventListener('submit', (e) => {
                    e.preventDefault();
                    this.handleLogin();
                });

                // 密码显示/隐藏
                document.getElementById('toggle-password').addEventListener('click', () => {
                    this.togglePassword();
                });

                // 回车键提交
                document.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter') {
                        e.preventDefault();
                        this.handleLogin();
                    }
                });
            }

            togglePassword() {
                const passwordInput = document.getElementById('password');
                const passwordIcon = document.getElementById('password-icon');
                
                if (passwordInput.type === 'password') {
                    passwordInput.type = 'text';
                    passwordIcon.className = 'fas fa-eye-slash';
                } else {
                    passwordInput.type = 'password';
                    passwordIcon.className = 'fas fa-eye';
                }
            }

            async checkExistingAuth() {
                const token = localStorage.getItem('access_token');
                if (token) {
                    // 验证token是否有效且具有管理员权限
                    try {
                        const response = await fetch('/api/admin/stats', {
                            headers: {
                                'Authorization': `Bearer ${token}`
                            }
                        });
                        
                        if (response.ok) {
                            // Token有效且有管理员权限，直接跳转
                            this.redirectToAdmin();
                            return;
                        }
                    } catch (error) {
                        // Token无效，清除
                        localStorage.removeItem('access_token');
                    }
                }
            }

            async handleLogin() {
                const username = document.getElementById('username').value.trim();
                const password = document.getElementById('password').value;
                const rememberMe = document.getElementById('remember-me').checked;

                if (!username || !password) {
                    this.showNotification('请输入用户名和密码', 'error');
                    return;
                }

                this.setLoading(true);

                try {
                    const response = await fetch('/api/auth/login', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({ username, password })
                    });

                    const data = await response.json();

                    if (response.ok) {
                        // 检查用户是否有管理员权限
                        if (data.user.role !== 'ADMIN') {
                            this.showNotification('您没有管理员权限', 'error');
                            return;
                        }

                        // 保存token
                        localStorage.setItem('access_token', data.access_token);
                        
                        // 如果选择记住我，设置更长的过期时间
                        if (rememberMe) {
                            localStorage.setItem('remember_login', 'true');
                        }

                        this.showNotification('登录成功，正在跳转...', 'success');
                        
                        // 延迟跳转，让用户看到成功消息
                        setTimeout(() => {
                            this.redirectToAdmin();
                        }, 1000);

                    } else {
                        this.showNotification(data.detail || '登录失败', 'error');
                    }
                } catch (error) {
                    console.error('登录错误:', error);
                    this.showNotification('网络错误，请重试', 'error');
                } finally {
                    this.setLoading(false);
                }
            }

            redirectToAdmin() {
                // 获取重定向URL，默认为admin页面
                const urlParams = new URLSearchParams(window.location.search);
                const redirectUrl = urlParams.get('redirect') || '/admin';
                window.location.href = redirectUrl;
            }

            setLoading(loading) {
                const loginBtn = document.getElementById('login-btn');
                const loginText = document.getElementById('login-text');
                const loginSpinner = document.getElementById('login-spinner');

                if (loading) {
                    loginBtn.disabled = true;
                    loginBtn.classList.add('opacity-75', 'cursor-not-allowed');
                    loginText.textContent = '登录中...';
                    loginSpinner.classList.remove('hidden');
                } else {
                    loginBtn.disabled = false;
                    loginBtn.classList.remove('opacity-75', 'cursor-not-allowed');
                    loginText.textContent = '登录';
                    loginSpinner.classList.add('hidden');
                }
            }

            showNotification(message, type = 'info') {
                const notification = document.getElementById('notification');
                notification.textContent = message;
                notification.className = `notification ${type}`;
                notification.classList.add('show');

                setTimeout(() => {
                    notification.classList.remove('show');
                }, 3000);
            }
        }

        // 初始化应用
        new LoginApp();
    </script>
</body>
</html>
