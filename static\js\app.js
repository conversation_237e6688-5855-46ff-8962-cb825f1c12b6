// 聊天平台前端应用
class ChatApp {
    constructor() {
        this.currentUser = null;
        this.currentRoom = null;
        this.socket = null;
        this.token = localStorage.getItem('access_token');

        this.initializeElements();
        this.bindEvents();
        this.checkAuthStatus();
    }

    initializeElements() {
        // 认证相关元素
        this.loginBtn = document.getElementById('login-btn');
        this.registerBtn = document.getElementById('register-btn');
        this.logoutBtn = document.getElementById('logout-btn');
        this.userInfo = document.getElementById('user-info');
        this.authButtons = document.getElementById('auth-buttons');
        this.userName = document.getElementById('user-name');

        // 模态框
        this.loginModal = document.getElementById('login-modal');
        this.registerModal = document.getElementById('register-modal');
        this.createRoomModal = document.getElementById('create-room-modal');
        this.loginForm = document.getElementById('login-form');
        this.registerForm = document.getElementById('register-form');
        this.createRoomForm = document.getElementById('create-room-form');

        // 聊天相关元素
        this.roomList = document.getElementById('room-list');
        this.createRoomBtn = document.getElementById('create-room-btn');
        this.welcomeScreen = document.getElementById('welcome-screen');
        this.chatArea = document.getElementById('chat-area');
        this.chatHeader = document.getElementById('chat-header');
        this.messagesContainer = document.getElementById('messages-container');
        this.messageInput = document.getElementById('message-input');
        this.sendBtn = document.getElementById('send-btn');
    }

    bindEvents() {
        // 认证事件
        this.loginBtn.addEventListener('click', () => this.showLoginModal());
        this.registerBtn.addEventListener('click', () => this.showRegisterModal());
        this.logoutBtn.addEventListener('click', () => this.logout());

        // 模态框事件
        document.getElementById('login-cancel').addEventListener('click', () => this.hideLoginModal());
        document.getElementById('register-cancel').addEventListener('click', () => this.hideRegisterModal());
        document.getElementById('create-room-close').addEventListener('click', () => this.hideCreateRoomModal());
        document.getElementById('create-room-cancel').addEventListener('click', () => this.hideCreateRoomModal());
        this.loginForm.addEventListener('submit', (e) => this.handleLogin(e));
        this.registerForm.addEventListener('submit', (e) => this.handleRegister(e));
        this.createRoomForm.addEventListener('submit', (e) => this.handleCreateRoom(e));

        // 房间类型选择事件
        document.getElementById('room-type').addEventListener('change', (e) => this.togglePasswordField(e));

        // 聊天事件
        this.sendBtn.addEventListener('click', () => this.sendMessage());
        this.messageInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.sendMessage();
            }
        });

        // 表情和文件事件
        document.getElementById('emoji-btn').addEventListener('click', () => this.toggleEmojiPicker());
        document.getElementById('file-btn').addEventListener('click', () => this.triggerFileInput());
        document.getElementById('file-input').addEventListener('change', (e) => this.handleFileSelect(e));

        // 创建房间事件
        this.createRoomBtn.addEventListener('click', () => this.showCreateRoomModal());

        // 邀请链接事件
        this.bindInviteEvents();

        // 表情选择器事件
        this.bindEmojiEvents();
    }

    bindInviteEvents() {
        // 邀请按钮点击事件
        document.getElementById('invite-btn').addEventListener('click', () => this.showInviteModal());

        // 复制邀请链接按钮
        document.getElementById('copy-invite-btn').addEventListener('click', () => this.copyInviteLink());

        // 关闭邀请模态框
        document.getElementById('close-invite-modal').addEventListener('click', () => this.hideInviteModal());
    }

    bindEmojiEvents() {
        // 绑定所有表情按钮的点击事件
        document.querySelectorAll('.emoji-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const emoji = e.target.getAttribute('data-emoji');
                this.insertEmoji(emoji);
            });
        });

        // 点击其他地方关闭表情选择器
        document.addEventListener('click', (e) => {
            const emojiPicker = document.getElementById('emoji-picker');
            const emojiBtn = document.getElementById('emoji-btn');

            if (!emojiPicker.contains(e.target) && !emojiBtn.contains(e.target)) {
                emojiPicker.classList.add('hidden');
            }
        });
    }

    async checkAuthStatus() {
        try {
            const response = await fetch('/api/auth/me', {
                headers: this.getAuthHeaders()
            });

            if (response.ok) {
                const data = await response.json();
                this.currentUser = data;
                this.updateUI();
                this.loadRooms();
            } else {
                // 尝试作为匿名用户
                const anonymousResponse = await fetch('/api/auth/me');
                if (anonymousResponse.ok) {
                    const data = await anonymousResponse.json();
                    this.currentUser = data;
                    this.updateUI();
                    this.loadRooms();
                }
            }
        } catch (error) {
            console.error('检查认证状态失败:', error);
        }
    }

    getAuthHeaders() {
        const headers = {
            'Content-Type': 'application/json'
        };
        if (this.token) {
            headers['Authorization'] = `Bearer ${this.token}`;
        }
        return headers;
    }

    updateUI() {
        if (this.currentUser) {
            if (this.currentUser.is_anonymous) {
                this.userName.textContent = this.currentUser.display_name || '匿名用户';
                this.authButtons.classList.remove('hidden');
                this.userInfo.classList.add('hidden');
                // 匿名用户禁用创建房间按钮
                this.createRoomBtn.classList.add('btn-disabled');
                this.createRoomBtn.title = '请先登录以创建房间';
            } else {
                this.userName.textContent = this.currentUser.username || this.currentUser.display_name;
                this.authButtons.classList.add('hidden');
                this.userInfo.classList.remove('hidden');
                // 已登录用户启用创建房间按钮
                this.createRoomBtn.classList.remove('btn-disabled');
                this.createRoomBtn.title = '创建新房间';
            }
        } else {
            // 未认证用户禁用创建房间按钮
            this.createRoomBtn.classList.add('btn-disabled');
            this.createRoomBtn.title = '请先登录以创建房间';
        }
    }

    showLoginModal() {
        this.loginModal.classList.remove('hidden');
        this.loginModal.classList.add('flex');
    }

    hideLoginModal() {
        this.loginModal.classList.add('hidden');
        this.loginModal.classList.remove('flex');
    }

    showRegisterModal() {
        this.registerModal.classList.remove('hidden');
        this.registerModal.classList.add('flex');
    }

    hideRegisterModal() {
        this.registerModal.classList.add('hidden');
        this.registerModal.classList.remove('flex');
    }

    async handleLogin(e) {
        e.preventDefault();
        const username = document.getElementById('login-username').value;
        const password = document.getElementById('login-password').value;

        try {
            const response = await fetch('/api/auth/login', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ username, password })
            });

            if (response.ok) {
                const data = await response.json();
                this.token = data.access_token;
                localStorage.setItem('access_token', this.token);
                this.currentUser = data.user;
                this.updateUI();
                this.hideLoginModal();
                this.loadRooms();
                this.showNotification('登录成功', 'success');
            } else {
                const error = await response.json();
                this.showNotification(error.detail || '登录失败', 'error');
            }
        } catch (error) {
            console.error('登录错误:', error);
            this.showNotification('登录失败，请重试', 'error');
        }
    }

    async handleRegister(e) {
        e.preventDefault();
        const username = document.getElementById('register-username').value;
        const password = document.getElementById('register-password').value;
        const confirmPassword = document.getElementById('register-confirm-password').value;

        // 验证密码确认
        if (password !== confirmPassword) {
            this.showNotification('两次输入的密码不一致', 'error');
            return;
        }

        try {
            const response = await fetch('/api/auth/register', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ username, password, confirm_password: confirmPassword })
            });

            if (response.ok) {
                const data = await response.json();
                this.token = data.access_token;
                localStorage.setItem('access_token', this.token);
                this.currentUser = data.user;
                this.updateUI();
                this.hideRegisterModal();
                this.loadRooms();
                this.showNotification('注册成功', 'success');
            } else {
                const error = await response.json();
                this.showNotification(error.detail || '注册失败', 'error');
            }
        } catch (error) {
            console.error('注册错误:', error);
            this.showNotification('注册失败，请重试', 'error');
        }
    }

    async logout() {
        try {
            // 调用后端登出API
            const response = await fetch('/api/auth/logout', {
                method: 'POST',
                headers: this.getAuthHeaders()
            });

            // 无论后端调用是否成功，都执行前端清理
            this.performLogoutCleanup();

            if (response.ok) {
                this.showNotification('已成功登出', 'success');
            } else {
                console.warn('后端登出API调用失败，但前端状态已清理');
                this.showNotification('已登出', 'info');
            }
        } catch (error) {
            console.error('登出过程中发生错误:', error);
            // 即使出错也要清理前端状态
            this.performLogoutCleanup();
            this.showNotification('已登出', 'info');
        }
    }

    performLogoutCleanup() {
        // 清除前端状态
        this.token = null;
        this.currentUser = null;
        this.currentRoom = null;

        // 清除localStorage
        localStorage.removeItem('access_token');

        // 清除所有可能的cookie
        this.clearAllCookies();

        // 重置UI状态
        this.resetUIToAnonymous();

        // 清空聊天区域
        this.showWelcomeScreen();

        // 重新检查认证状态（这会创建新的匿名用户）
        setTimeout(() => {
            this.checkAuthStatus();
        }, 100);
    }

    clearAllCookies() {
        // 获取所有可能的cookie路径和域名组合
        const paths = ['/', '/api', '/api/auth'];
        const domains = [window.location.hostname, '.' + window.location.hostname, 'localhost', '.localhost'];

        // 清除anonymous_id cookie的所有可能组合
        paths.forEach(path => {
            // 不指定域名
            document.cookie = `anonymous_id=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=${path};`;
            document.cookie = `anonymous_id=; max-age=0; path=${path};`;

            // 指定各种域名
            domains.forEach(domain => {
                document.cookie = `anonymous_id=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=${path}; domain=${domain};`;
                document.cookie = `anonymous_id=; max-age=0; path=${path}; domain=${domain};`;
            });
        });
    }

    resetUIToAnonymous() {
        // 显示登录/注册按钮，隐藏用户信息
        if (this.authButtons) {
            this.authButtons.classList.remove('hidden');
        }
        if (this.userInfo) {
            this.userInfo.classList.add('hidden');
        }

        // 禁用创建房间按钮
        if (this.createRoomBtn) {
            this.createRoomBtn.classList.add('btn-disabled');
            this.createRoomBtn.title = '请先登录以创建房间';
        }

        // 清空用户名显示
        if (this.userName) {
            this.userName.textContent = '';
        }
    }

    async loadRooms() {
        try {
            const response = await fetch('/api/rooms/', {
                headers: this.getAuthHeaders()
            });

            if (response.ok) {
                const rooms = await response.json();
                this.displayRooms(rooms);
            }
        } catch (error) {
            console.error('加载房间列表失败:', error);
        }
    }

    displayRooms(rooms) {
        this.roomList.innerHTML = '';

        rooms.forEach(room => {
            const roomElement = document.createElement('div');
            roomElement.className = 'p-3 bg-gray-50 rounded-lg cursor-pointer hover:bg-gray-100 transition-colors';
            roomElement.innerHTML = `
                <div class="font-medium text-gray-900">${room.name}</div>
                <div class="text-sm text-gray-500">${room.member_count || 0} 人</div>
            `;
            roomElement.addEventListener('click', () => this.joinRoom(room));
            this.roomList.appendChild(roomElement);
        });
    }

    showCreateRoomModal() {
        // 检查用户是否已登录且不是匿名用户
        if (!this.currentUser || this.currentUser.is_anonymous) {
            this.showNotification('请先登录以创建房间', 'warning');
            this.showLoginModal();
            return;
        }

        this.createRoomModal.classList.remove('hidden');
        this.createRoomModal.classList.add('flex');
        // 重置表单
        this.createRoomForm.reset();
        document.getElementById('room-max-members').value = '100';
        document.getElementById('room-type').value = 'private';
        this.togglePasswordField({ target: { value: 'private' } });
    }

    hideCreateRoomModal() {
        this.createRoomModal.classList.add('hidden');
        this.createRoomModal.classList.remove('flex');
    }

    togglePasswordField(e) {
        const passwordField = document.getElementById('password-field');
        if (e.target.value === 'password') {
            passwordField.classList.remove('hidden');
            document.getElementById('room-password').required = true;
        } else {
            passwordField.classList.add('hidden');
            document.getElementById('room-password').required = false;
        }
    }

    async handleCreateRoom(e) {
        e.preventDefault();

        // 获取表单数据
        const roomNameElement = document.getElementById('room-name');
        const roomDescriptionElement = document.getElementById('room-description');
        const roomTypeElement = document.getElementById('room-type');
        const maxMembersElement = document.getElementById('room-max-members');
        const roomPasswordElement = document.getElementById('room-password');

        if (!roomNameElement || !roomDescriptionElement || !roomTypeElement || !maxMembersElement) {
            this.showNotification('表单元素未找到，请刷新页面重试', 'error');
            return;
        }

        const roomName = roomNameElement.value ? roomNameElement.value.trim() : '';
        const roomDescription = roomDescriptionElement.value ? roomDescriptionElement.value.trim() : '';
        const roomType = roomTypeElement.value || 'public';
        const maxMembers = parseInt(maxMembersElement.value) || 100;
        const roomPassword = roomPasswordElement ? roomPasswordElement.value : '';

        // 验证必填字段
        if (!roomName) {
            this.showNotification('请输入房间名称', 'error');
            return;
        }

        if (maxMembers < 2 || maxMembers > 1000) {
            this.showNotification('最大成员数必须在2-1000之间', 'error');
            return;
        }

        if (roomType === 'password' && !roomPassword) {
            this.showNotification('密码房间必须设置密码', 'error');
            return;
        }

        const roomData = {
            name: roomName,
            description: roomDescription || null,
            room_type: roomType,
            max_members: maxMembers
        };

        // 如果是密码房间，添加密码
        if (roomType === 'password') {
            roomData.password = roomPassword;
        }

        console.log('创建房间数据:', roomData);
        console.log('当前token:', this.token ? `${this.token.substring(0, 20)}...` : 'null');
        console.log('当前用户:', this.currentUser);

        try {
            const response = await fetch('/api/rooms/', {
                method: 'POST',
                headers: {
                    ...this.getAuthHeaders(),
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(roomData)
            });

            if (response.ok) {
                const room = await response.json();
                this.hideCreateRoomModal();
                this.loadRooms();
                this.showNotification('房间创建成功', 'success');
            } else {
                const error = await response.json();
                console.error('创建房间失败，响应状态:', response.status);
                console.error('错误详情:', error);

                let errorMessage = '创建房间失败';

                if (error.detail) {
                    if (Array.isArray(error.detail)) {
                        // Pydantic验证错误
                        const validationErrors = error.detail.map(err => {
                            const field = err.loc ? err.loc.join('.') : '未知字段';
                            return `${field}: ${err.msg}`;
                        }).join(', ');
                        errorMessage = `验证错误: ${validationErrors}`;
                    } else if (typeof error.detail === 'string') {
                        errorMessage = error.detail;
                    }
                }

                this.showNotification(errorMessage, 'error');
            }
        } catch (error) {
            console.error('创建房间错误:', error);
            this.showNotification('创建房间失败，请重试', 'error');
        }
    }

    async joinRoom(room) {
        try {
            const response = await fetch(`/api/rooms/${room.id}/join`, {
                method: 'POST',
                headers: {
                    ...this.getAuthHeaders(),
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({}) // 发送空的请求体以满足API要求
            });

            if (response.ok) {
                const result = await response.json();
                // 如果后端返回了房间信息，使用后端的数据；否则使用传入的room对象
                this.currentRoom = result.room || room;
                this.showChatArea();
                this.loadMessages();
                this.connectWebSocket();

                // 如果有消息，显示通知
                if (result.message) {
                    this.showNotification(result.message, 'success');
                }
            } else {
                const error = await response.json();
                this.showNotification(error.detail || '加入房间失败', 'error');
            }
        } catch (error) {
            console.error('加入房间错误:', error);
            this.showNotification('加入房间失败，请重试', 'error');
        }
    }

    showWelcomeScreen() {
        this.welcomeScreen.classList.remove('hidden');
        this.chatArea.classList.add('hidden');
        this.chatHeader.classList.add('hidden');
    }

    showChatArea() {
        this.welcomeScreen.classList.add('hidden');
        this.chatArea.classList.remove('hidden');
        this.chatHeader.classList.remove('hidden');

        document.getElementById('current-room-name').textContent = this.currentRoom.name;
        document.getElementById('room-members').textContent = `${this.currentRoom.member_count || 0} 人在线`;

        // 检查是否显示邀请按钮（仅房主和管理员可见）
        this.checkInvitePermission();
    }

    async loadMessages() {
        try {
            const response = await fetch(`/api/rooms/${this.currentRoom.id}/messages`, {
                headers: this.getAuthHeaders()
            });

            if (response.ok) {
                const messages = await response.json();
                this.displayMessages(messages);
            }
        } catch (error) {
            console.error('加载消息失败:', error);
        }
    }

    displayMessages(messages) {
        this.messagesContainer.innerHTML = '';

        messages.forEach(message => {
            this.addMessageToUI(message);
        });

        this.scrollToBottom();
    }

    replaceTemporaryMessage(tempId, realMessage) {
        const tempElement = document.querySelector(`[data-message-id="${tempId}"]`);
        if (tempElement) {
            tempElement.setAttribute('data-message-id', realMessage.id);
            tempElement.setAttribute('data-status', 'sent');
            // 移除发送中的状态指示器
            const statusIndicator = tempElement.querySelector('.message-status');
            if (statusIndicator) {
                statusIndicator.remove();
            }
        }
    }

    markMessageAsFailed(tempId, originalContent) {
        const tempElement = document.querySelector(`[data-message-id="${tempId}"]`);
        if (tempElement) {
            tempElement.setAttribute('data-status', 'failed');
            tempElement.setAttribute('data-original-content', originalContent);

            // 添加重试按钮
            const messageContent = tempElement.querySelector('.message-content');
            if (messageContent && !messageContent.querySelector('.retry-btn')) {
                const retryBtn = document.createElement('button');
                retryBtn.className = 'retry-btn ml-2 text-red-500 hover:text-red-700 transition-colors';
                retryBtn.innerHTML = '⚠️';
                retryBtn.title = '点击重试发送';
                retryBtn.onclick = () => this.retryMessage(tempId, originalContent);
                messageContent.appendChild(retryBtn);
            }
        }
    }

    async retryMessage(messageId, content) {
        const messageElement = document.querySelector(`[data-message-id="${messageId}"]`);
        const retryBtn = messageElement?.querySelector('.retry-btn');

        if (retryBtn) {
            // 禁用重试按钮2秒
            retryBtn.disabled = true;
            retryBtn.innerHTML = '🚫';
            retryBtn.title = '请等待2秒后重试';

            setTimeout(() => {
                if (retryBtn) {
                    retryBtn.disabled = false;
                    retryBtn.innerHTML = '⚠️';
                    retryBtn.title = '点击重试发送';
                }
            }, 2000);
        }

        // 重新发送消息
        try {
            const response = await fetch(`/api/rooms/${this.currentRoom.id}/messages`, {
                method: 'POST',
                headers: this.getAuthHeaders(),
                body: JSON.stringify({ content, type: 'text' })
            });

            if (response.ok) {
                const realMessage = await response.json();
                // 替换失败消息为成功消息
                this.replaceTemporaryMessage(messageId, realMessage);
                this.showNotification('消息发送成功', 'success');
            } else {
                const error = await response.json();
                this.showNotification(error.detail || '重试发送失败', 'error');
            }
        } catch (error) {
            console.error('重试发送消息错误:', error);
            this.showNotification('重试发送失败，请检查网络连接', 'error');
        }
    }

    addMessageToUI(message) {
        const messageElement = document.createElement('div');
        const isOwn = message.sender_id === this.currentUser.id;

        messageElement.className = `flex ${isOwn ? 'justify-end' : 'justify-start'}`;
        messageElement.setAttribute('data-message-id', message.id);
        messageElement.setAttribute('data-status', message.status || 'sent');

        // 根据消息类型生成不同的内容
        let messageContent = '';

        if (message.message_type === 'image' || message.type === 'image') {
            messageContent = `
                <div class="message-image">
                    <img src="${message.content}" alt="图片" class="max-w-xs max-h-64 rounded-lg cursor-pointer" onclick="this.requestFullscreen()">
                    ${message.file_name ? `<div class="text-xs mt-1 opacity-75">${message.file_name}</div>` : ''}
                </div>
            `;
        } else if (message.message_type === 'audio' || message.type === 'audio') {
            messageContent = `
                <div class="message-audio">
                    <audio controls class="max-w-xs">
                        <source src="${message.content}" type="audio/mpeg">
                        您的浏览器不支持音频播放。
                    </audio>
                    ${message.file_name ? `<div class="text-xs mt-1 opacity-75">${message.file_name}</div>` : ''}
                </div>
            `;
        } else {
            // 文本消息
            messageContent = `<span>${this.escapeHtml(message.content)}</span>`;
        }

        messageElement.innerHTML = `
            <div class="message-bubble ${isOwn ? 'own text-white' : 'other'} px-4 py-2 rounded-lg">
                ${!isOwn ? `<div class="text-xs text-gray-500 mb-1">${message.sender_display_name || message.sender_username || '匿名用户'}</div>` : ''}
                <div class="message-content flex items-center">
                    ${messageContent}
                    ${message.status === 'sending' ? '<span class="message-status ml-2 text-gray-400">发送中...</span>' : ''}
                    ${message.status === 'uploading' ? '<span class="message-status ml-2 text-gray-400">上传中...</span>' : ''}
                </div>
                <div class="text-xs ${isOwn ? 'text-gray-200' : 'text-gray-400'} mt-1">
                    ${new Date(message.created_at).toLocaleTimeString()}
                </div>
            </div>
        `;

        this.messagesContainer.appendChild(messageElement);
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    async sendMessage() {
        const content = this.messageInput.value.trim();
        if (!content || !this.currentRoom) return;

        // 生成临时消息ID
        const tempMessageId = 'temp_' + Date.now();

        // 立即显示消息（发送中状态）
        const tempMessage = {
            id: tempMessageId,
            content: content,
            sender_id: this.currentUser.id,
            sender_username: this.currentUser.display_identifier || this.currentUser.username,
            sender_display_name: this.currentUser.display_name,
            message_type: 'text',
            created_at: new Date().toISOString(),
            status: 'sending'
        };

        this.addMessageToUI(tempMessage);
        this.messageInput.value = '';
        this.scrollToBottom();

        try {
            const response = await fetch(`/api/rooms/${this.currentRoom.id}/messages`, {
                method: 'POST',
                headers: this.getAuthHeaders(),
                body: JSON.stringify({ content, type: 'text' })
            });

            if (response.ok) {
                const realMessage = await response.json();
                // 替换临时消息为真实消息
                this.replaceTemporaryMessage(tempMessageId, realMessage);
            } else {
                const error = await response.json();
                // 标记消息为发送失败
                this.markMessageAsFailed(tempMessageId, content);
                this.showNotification(error.detail || '发送消息失败', 'error');
            }
        } catch (error) {
            console.error('发送消息错误:', error);
            // 标记消息为发送失败
            this.markMessageAsFailed(tempMessageId, content);
            this.showNotification('发送消息失败，请重试', 'error');
        }
    }

    connectWebSocket() {
        if (this.socket) {
            this.socket.close();
        }

        const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        const wsUrl = `${protocol}//${window.location.host}/ws/${this.currentRoom.id}`;

        this.socket = new WebSocket(wsUrl);

        this.socket.onmessage = (event) => {
            const message = JSON.parse(event.data);
            this.addMessageToUI(message);
            this.scrollToBottom();
        };

        this.socket.onerror = (error) => {
            console.error('WebSocket错误:', error);
        };
    }

    scrollToBottom() {
        this.messagesContainer.scrollTop = this.messagesContainer.scrollHeight;
    }

    showNotification(message, type = 'info') {
        // 简单的通知实现
        const notification = document.createElement('div');
        notification.className = `fixed top-4 right-4 px-4 py-2 rounded-md text-white z-50 ${
            type === 'success' ? 'bg-green-500' :
            type === 'error' ? 'bg-red-500' :
            'bg-blue-500'
        }`;
        notification.textContent = message;

        document.body.appendChild(notification);

        setTimeout(() => {
            notification.remove();
        }, 3000);
    }

    async checkInvitePermission() {
        const inviteBtn = document.getElementById('invite-btn');

        // 只有登录用户才可能有邀请权限
        if (!this.currentUser || this.currentUser.is_anonymous) {
            inviteBtn.classList.add('hidden');
            return;
        }

        // 检查用户在当前房间的角色
        try {
            const response = await fetch(`/api/rooms/${this.currentRoom.id}/invite`, {
                headers: this.getAuthHeaders()
            });

            if (response.ok) {
                // 用户有权限获取邀请链接，显示按钮
                inviteBtn.classList.remove('hidden');
            } else {
                // 用户没有权限，隐藏按钮
                inviteBtn.classList.add('hidden');
            }
        } catch (error) {
            console.error('检查邀请权限错误:', error);
            inviteBtn.classList.add('hidden');
        }
    }

    async showInviteModal() {
        try {
            const response = await fetch(`/api/rooms/${this.currentRoom.id}/invite`, {
                headers: this.getAuthHeaders()
            });

            if (response.ok) {
                const data = await response.json();
                const inviteUrl = `${window.location.origin}/invite/${data.invite_code}`;

                document.getElementById('invite-url').value = inviteUrl;
                document.getElementById('invite-modal').classList.remove('hidden');
            } else {
                const error = await response.json();
                this.showNotification(error.detail || '获取邀请链接失败', 'error');
            }
        } catch (error) {
            console.error('获取邀请链接错误:', error);
            this.showNotification('获取邀请链接失败，请重试', 'error');
        }
    }

    hideInviteModal() {
        document.getElementById('invite-modal').classList.add('hidden');
    }

    async copyInviteLink() {
        const inviteUrl = document.getElementById('invite-url').value;
        const copyBtn = document.getElementById('copy-invite-btn');

        try {
            await navigator.clipboard.writeText(inviteUrl);

            // 临时改变按钮文本
            const originalHtml = copyBtn.innerHTML;
            copyBtn.innerHTML = '<i class="fas fa-check"></i>';
            copyBtn.classList.remove('bg-blue-500', 'hover:bg-blue-600');
            copyBtn.classList.add('bg-green-500');

            setTimeout(() => {
                copyBtn.innerHTML = originalHtml;
                copyBtn.classList.remove('bg-green-500');
                copyBtn.classList.add('bg-blue-500', 'hover:bg-blue-600');
            }, 2000);

            this.showNotification('邀请链接已复制到剪贴板', 'success');
        } catch (error) {
            console.error('复制失败:', error);
            this.showNotification('复制失败，请手动复制', 'error');
        }
    }

    // 表情选择器功能
    toggleEmojiPicker() {
        const emojiPicker = document.getElementById('emoji-picker');
        emojiPicker.classList.toggle('hidden');
    }

    insertEmoji(emoji) {
        const messageInput = document.getElementById('message-input');
        const currentValue = messageInput.value;
        const cursorPosition = messageInput.selectionStart;

        // 在光标位置插入表情
        const newValue = currentValue.slice(0, cursorPosition) + emoji + currentValue.slice(cursorPosition);
        messageInput.value = newValue;

        // 设置光标位置到表情后面
        const newCursorPosition = cursorPosition + emoji.length;
        messageInput.setSelectionRange(newCursorPosition, newCursorPosition);

        // 聚焦到输入框
        messageInput.focus();

        // 隐藏表情选择器
        document.getElementById('emoji-picker').classList.add('hidden');
    }

    // 文件上传功能
    triggerFileInput() {
        document.getElementById('file-input').click();
    }

    async handleFileSelect(event) {
        const file = event.target.files[0];
        if (!file) return;

        // 检查文件大小 (限制为10MB)
        const maxSize = 10 * 1024 * 1024; // 10MB
        if (file.size > maxSize) {
            this.showNotification('文件大小不能超过10MB', 'error');
            return;
        }

        // 检查文件类型
        const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'audio/mpeg', 'audio/wav', 'audio/ogg'];
        if (!allowedTypes.includes(file.type)) {
            this.showNotification('不支持的文件类型，仅支持图片和音频文件', 'error');
            return;
        }

        try {
            await this.uploadFile(file);
        } catch (error) {
            console.error('文件上传错误:', error);
            this.showNotification('文件上传失败，请重试', 'error');
        }

        // 清空文件输入
        event.target.value = '';
    }

    async uploadFile(file) {
        if (!this.currentRoom) {
            this.showNotification('请先加入房间', 'warning');
            return;
        }

        const formData = new FormData();
        formData.append('file', file);

        // 显示上传进度
        const tempMessageId = 'temp_' + Date.now();
        const tempMessage = {
            id: tempMessageId,
            content: `正在上传文件: ${file.name}`,
            sender_id: this.currentUser.id,
            sender_username: this.currentUser.display_identifier || this.currentUser.username,
            sender_display_name: this.currentUser.display_name,
            message_type: 'file',
            created_at: new Date().toISOString(),
            status: 'uploading'
        };

        this.addMessageToUI(tempMessage);
        this.scrollToBottom();

        try {
            const response = await fetch(`/api/rooms/${this.currentRoom.id}/upload`, {
                method: 'POST',
                headers: this.getAuthHeaders(),
                body: formData
            });

            if (response.ok) {
                const result = await response.json();

                // 发送文件消息
                const messageResponse = await fetch(`/api/rooms/${this.currentRoom.id}/messages`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        ...this.getAuthHeaders()
                    },
                    body: JSON.stringify({
                        content: result.file_url,
                        type: file.type.startsWith('image/') ? 'image' : 'audio',
                        file_name: file.name,
                        file_size: file.size
                    })
                });

                if (messageResponse.ok) {
                    const messageData = await messageResponse.json();
                    this.replaceTemporaryMessage(tempMessageId, messageData);
                } else {
                    throw new Error('发送文件消息失败');
                }
            } else {
                const error = await response.json();
                throw new Error(error.detail || '文件上传失败');
            }
        } catch (error) {
            console.error('文件上传错误:', error);
            this.markMessageAsFailed(tempMessageId, error.message);
        }
    }
}

// 初始化应用
document.addEventListener('DOMContentLoaded', () => {
    new ChatApp();
});