// 聊天平台前端应用
class ChatApp {
    constructor() {
        this.currentUser = null;
        this.currentRoom = null;
        this.socket = null;
        this.token = localStorage.getItem('access_token');

        this.initializeElements();
        this.bindEvents();
        this.checkAuthStatus();
    }

    initializeElements() {
        // 认证相关元素
        this.loginBtn = document.getElementById('login-btn');
        this.registerBtn = document.getElementById('register-btn');
        this.logoutBtn = document.getElementById('logout-btn');
        this.userInfo = document.getElementById('user-info');
        this.authButtons = document.getElementById('auth-buttons');
        this.userName = document.getElementById('user-name');

        // 模态框
        this.loginModal = document.getElementById('login-modal');
        this.registerModal = document.getElementById('register-modal');
        this.loginForm = document.getElementById('login-form');
        this.registerForm = document.getElementById('register-form');

        // 聊天相关元素
        this.roomList = document.getElementById('room-list');
        this.createRoomBtn = document.getElementById('create-room-btn');
        this.welcomeScreen = document.getElementById('welcome-screen');
        this.chatArea = document.getElementById('chat-area');
        this.chatHeader = document.getElementById('chat-header');
        this.messagesContainer = document.getElementById('messages-container');
        this.messageInput = document.getElementById('message-input');
        this.sendBtn = document.getElementById('send-btn');
    }

    bindEvents() {
        // 认证事件
        this.loginBtn.addEventListener('click', () => this.showLoginModal());
        this.registerBtn.addEventListener('click', () => this.showRegisterModal());
        this.logoutBtn.addEventListener('click', () => this.logout());

        // 模态框事件
        document.getElementById('login-cancel').addEventListener('click', () => this.hideLoginModal());
        document.getElementById('register-cancel').addEventListener('click', () => this.hideRegisterModal());
        this.loginForm.addEventListener('submit', (e) => this.handleLogin(e));
        this.registerForm.addEventListener('submit', (e) => this.handleRegister(e));

        // 聊天事件
        this.sendBtn.addEventListener('click', () => this.sendMessage());
        this.messageInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.sendMessage();
            }
        });

        // 创建房间事件
        this.createRoomBtn.addEventListener('click', () => this.createRoom());
    }

    async checkAuthStatus() {
        try {
            const response = await fetch('/api/auth/me', {
                headers: this.getAuthHeaders()
            });

            if (response.ok) {
                const data = await response.json();
                this.currentUser = data;
                this.updateUI();
                this.loadRooms();
            } else {
                // 尝试作为匿名用户
                const anonymousResponse = await fetch('/api/auth/me');
                if (anonymousResponse.ok) {
                    const data = await anonymousResponse.json();
                    this.currentUser = data;
                    this.updateUI();
                    this.loadRooms();
                }
            }
        } catch (error) {
            console.error('检查认证状态失败:', error);
        }
    }

    getAuthHeaders() {
        const headers = {
            'Content-Type': 'application/json'
        };
        if (this.token) {
            headers['Authorization'] = `Bearer ${this.token}`;
        }
        return headers;
    }

    updateUI() {
        if (this.currentUser) {
            if (this.currentUser.is_anonymous) {
                this.userName.textContent = '匿名用户';
                this.authButtons.classList.remove('hidden');
                this.userInfo.classList.add('hidden');
            } else {
                this.userName.textContent = this.currentUser.username || this.currentUser.display_name;
                this.authButtons.classList.add('hidden');
                this.userInfo.classList.remove('hidden');
            }
        }
    }

    showLoginModal() {
        this.loginModal.classList.remove('hidden');
        this.loginModal.classList.add('flex');
    }

    hideLoginModal() {
        this.loginModal.classList.add('hidden');
        this.loginModal.classList.remove('flex');
    }

    showRegisterModal() {
        this.registerModal.classList.remove('hidden');
        this.registerModal.classList.add('flex');
    }

    hideRegisterModal() {
        this.registerModal.classList.add('hidden');
        this.registerModal.classList.remove('flex');
    }

    async handleLogin(e) {
        e.preventDefault();
        const username = document.getElementById('login-username').value;
        const password = document.getElementById('login-password').value;

        try {
            const response = await fetch('/api/auth/login', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ username, password })
            });

            if (response.ok) {
                const data = await response.json();
                this.token = data.access_token;
                localStorage.setItem('access_token', this.token);
                this.currentUser = data.user;
                this.updateUI();
                this.hideLoginModal();
                this.loadRooms();
                this.showNotification('登录成功', 'success');
            } else {
                const error = await response.json();
                this.showNotification(error.detail || '登录失败', 'error');
            }
        } catch (error) {
            console.error('登录错误:', error);
            this.showNotification('登录失败，请重试', 'error');
        }
    }

    async handleRegister(e) {
        e.preventDefault();
        const username = document.getElementById('register-username').value;
        const email = document.getElementById('register-email').value;
        const password = document.getElementById('register-password').value;

        try {
            const response = await fetch('/api/auth/register', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ username, email, password })
            });

            if (response.ok) {
                const data = await response.json();
                this.token = data.access_token;
                localStorage.setItem('access_token', this.token);
                this.currentUser = data.user;
                this.updateUI();
                this.hideRegisterModal();
                this.loadRooms();
                this.showNotification('注册成功', 'success');
            } else {
                const error = await response.json();
                this.showNotification(error.detail || '注册失败', 'error');
            }
        } catch (error) {
            console.error('注册错误:', error);
            this.showNotification('注册失败，请重试', 'error');
        }
    }

    logout() {
        this.token = null;
        localStorage.removeItem('access_token');
        this.currentUser = null;
        this.currentRoom = null;

        // 重新检查认证状态（会创建匿名用户）
        this.checkAuthStatus();

        // 清空聊天区域
        this.showWelcomeScreen();

        this.showNotification('已登出', 'info');
    }

    async loadRooms() {
        try {
            const response = await fetch('/api/rooms/', {
                headers: this.getAuthHeaders()
            });

            if (response.ok) {
                const rooms = await response.json();
                this.displayRooms(rooms);
            }
        } catch (error) {
            console.error('加载房间列表失败:', error);
        }
    }

    displayRooms(rooms) {
        this.roomList.innerHTML = '';

        rooms.forEach(room => {
            const roomElement = document.createElement('div');
            roomElement.className = 'p-3 bg-gray-50 rounded-lg cursor-pointer hover:bg-gray-100 transition-colors';
            roomElement.innerHTML = `
                <div class="font-medium text-gray-900">${room.name}</div>
                <div class="text-sm text-gray-500">${room.member_count || 0} 人</div>
            `;
            roomElement.addEventListener('click', () => this.joinRoom(room));
            this.roomList.appendChild(roomElement);
        });
    }

    async createRoom() {
        const name = prompt('请输入房间名称:');
        if (!name) return;

        try {
            const response = await fetch('/api/rooms/', {
                method: 'POST',
                headers: this.getAuthHeaders(),
                body: JSON.stringify({ name, type: 'public' })
            });

            if (response.ok) {
                const room = await response.json();
                this.loadRooms();
                this.showNotification('房间创建成功', 'success');
            } else {
                const error = await response.json();
                this.showNotification(error.detail || '创建房间失败', 'error');
            }
        } catch (error) {
            console.error('创建房间错误:', error);
            this.showNotification('创建房间失败，请重试', 'error');
        }
    }

    async joinRoom(room) {
        try {
            const response = await fetch(`/api/rooms/${room.id}/join`, {
                method: 'POST',
                headers: this.getAuthHeaders()
            });

            if (response.ok) {
                this.currentRoom = room;
                this.showChatArea();
                this.loadMessages();
                this.connectWebSocket();
            } else {
                const error = await response.json();
                this.showNotification(error.detail || '加入房间失败', 'error');
            }
        } catch (error) {
            console.error('加入房间错误:', error);
            this.showNotification('加入房间失败，请重试', 'error');
        }
    }

    showWelcomeScreen() {
        this.welcomeScreen.classList.remove('hidden');
        this.chatArea.classList.add('hidden');
        this.chatHeader.classList.add('hidden');
    }

    showChatArea() {
        this.welcomeScreen.classList.add('hidden');
        this.chatArea.classList.remove('hidden');
        this.chatHeader.classList.remove('hidden');

        document.getElementById('room-name').textContent = this.currentRoom.name;
        document.getElementById('room-members').textContent = `${this.currentRoom.member_count || 0} 人在线`;
    }

    async loadMessages() {
        try {
            const response = await fetch(`/api/rooms/${this.currentRoom.id}/messages`, {
                headers: this.getAuthHeaders()
            });

            if (response.ok) {
                const messages = await response.json();
                this.displayMessages(messages);
            }
        } catch (error) {
            console.error('加载消息失败:', error);
        }
    }

    displayMessages(messages) {
        this.messagesContainer.innerHTML = '';

        messages.forEach(message => {
            this.addMessageToUI(message);
        });

        this.scrollToBottom();
    }

    addMessageToUI(message) {
        const messageElement = document.createElement('div');
        const isOwn = message.sender_id === this.currentUser.id;

        messageElement.className = `flex ${isOwn ? 'justify-end' : 'justify-start'}`;
        messageElement.innerHTML = `
            <div class="message-bubble ${isOwn ? 'own text-white' : 'other'} px-4 py-2 rounded-lg">
                ${!isOwn ? `<div class="text-xs text-gray-500 mb-1">${message.sender_name || '匿名用户'}</div>` : ''}
                <div>${message.content}</div>
                <div class="text-xs ${isOwn ? 'text-gray-200' : 'text-gray-400'} mt-1">
                    ${new Date(message.created_at).toLocaleTimeString()}
                </div>
            </div>
        `;

        this.messagesContainer.appendChild(messageElement);
    }

    async sendMessage() {
        const content = this.messageInput.value.trim();
        if (!content || !this.currentRoom) return;

        try {
            const response = await fetch(`/api/rooms/${this.currentRoom.id}/messages`, {
                method: 'POST',
                headers: this.getAuthHeaders(),
                body: JSON.stringify({ content, type: 'text' })
            });

            if (response.ok) {
                this.messageInput.value = '';
            } else {
                const error = await response.json();
                this.showNotification(error.detail || '发送消息失败', 'error');
            }
        } catch (error) {
            console.error('发送消息错误:', error);
            this.showNotification('发送消息失败，请重试', 'error');
        }
    }

    connectWebSocket() {
        if (this.socket) {
            this.socket.close();
        }

        const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        const wsUrl = `${protocol}//${window.location.host}/ws/${this.currentRoom.id}`;

        this.socket = new WebSocket(wsUrl);

        this.socket.onmessage = (event) => {
            const message = JSON.parse(event.data);
            this.addMessageToUI(message);
            this.scrollToBottom();
        };

        this.socket.onerror = (error) => {
            console.error('WebSocket错误:', error);
        };
    }

    scrollToBottom() {
        this.messagesContainer.scrollTop = this.messagesContainer.scrollHeight;
    }

    showNotification(message, type = 'info') {
        // 简单的通知实现
        const notification = document.createElement('div');
        notification.className = `fixed top-4 right-4 px-4 py-2 rounded-md text-white z-50 ${
            type === 'success' ? 'bg-green-500' :
            type === 'error' ? 'bg-red-500' :
            'bg-blue-500'
        }`;
        notification.textContent = message;

        document.body.appendChild(notification);

        setTimeout(() => {
            notification.remove();
        }, 3000);
    }
}

// 初始化应用
document.addEventListener('DOMContentLoaded', () => {
    new ChatApp();
});