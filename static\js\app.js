// 聊天平台前端应用
class ChatApp {
    constructor() {
        this.currentUser = null;
        this.currentRoom = null;
        this.socket = null;
        this.token = localStorage.getItem('access_token');

        this.initializeElements();
        this.bindEvents();
        this.checkAuthStatus();
    }

    initializeElements() {
        // 认证相关元素
        this.loginBtn = document.getElementById('login-btn');
        this.registerBtn = document.getElementById('register-btn');
        this.logoutBtn = document.getElementById('logout-btn');
        this.userInfo = document.getElementById('user-info');
        this.authButtons = document.getElementById('auth-buttons');
        this.userName = document.getElementById('user-name');

        // 模态框
        this.loginModal = document.getElementById('login-modal');
        this.registerModal = document.getElementById('register-modal');
        this.createRoomModal = document.getElementById('create-room-modal');
        this.loginForm = document.getElementById('login-form');
        this.registerForm = document.getElementById('register-form');
        this.createRoomForm = document.getElementById('create-room-form');

        // 聊天相关元素
        this.roomList = document.getElementById('room-list');
        this.createRoomBtn = document.getElementById('create-room-btn');
        this.welcomeScreen = document.getElementById('welcome-screen');
        this.chatArea = document.getElementById('chat-area');
        this.chatHeader = document.getElementById('chat-header');
        this.messagesContainer = document.getElementById('messages-container');
        this.messageInput = document.getElementById('message-input');
        this.sendBtn = document.getElementById('send-btn');
    }

    bindEvents() {
        // 认证事件
        this.loginBtn.addEventListener('click', () => this.showLoginModal());
        this.registerBtn.addEventListener('click', () => this.showRegisterModal());
        this.logoutBtn.addEventListener('click', () => this.logout());

        // 模态框事件
        document.getElementById('login-cancel').addEventListener('click', () => this.hideLoginModal());
        document.getElementById('register-cancel').addEventListener('click', () => this.hideRegisterModal());
        document.getElementById('create-room-close').addEventListener('click', () => this.hideCreateRoomModal());
        document.getElementById('create-room-cancel').addEventListener('click', () => this.hideCreateRoomModal());
        this.loginForm.addEventListener('submit', (e) => this.handleLogin(e));
        this.registerForm.addEventListener('submit', (e) => this.handleRegister(e));
        this.createRoomForm.addEventListener('submit', (e) => this.handleCreateRoom(e));

        // 房间类型选择事件
        document.getElementById('room-type').addEventListener('change', (e) => this.togglePasswordField(e));

        // 聊天事件
        this.sendBtn.addEventListener('click', () => this.sendMessage());
        this.messageInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.sendMessage();
            }
        });

        // 创建房间事件
        this.createRoomBtn.addEventListener('click', () => this.showCreateRoomModal());
    }

    async checkAuthStatus() {
        try {
            const response = await fetch('/api/auth/me', {
                headers: this.getAuthHeaders()
            });

            if (response.ok) {
                const data = await response.json();
                this.currentUser = data;
                this.updateUI();
                this.loadRooms();
            } else {
                // 尝试作为匿名用户
                const anonymousResponse = await fetch('/api/auth/me');
                if (anonymousResponse.ok) {
                    const data = await anonymousResponse.json();
                    this.currentUser = data;
                    this.updateUI();
                    this.loadRooms();
                }
            }
        } catch (error) {
            console.error('检查认证状态失败:', error);
        }
    }

    getAuthHeaders() {
        const headers = {
            'Content-Type': 'application/json'
        };
        if (this.token) {
            headers['Authorization'] = `Bearer ${this.token}`;
        }
        return headers;
    }

    updateUI() {
        if (this.currentUser) {
            if (this.currentUser.is_anonymous) {
                this.userName.textContent = this.currentUser.display_name || '匿名用户';
                this.authButtons.classList.remove('hidden');
                this.userInfo.classList.add('hidden');
                // 匿名用户禁用创建房间按钮
                this.createRoomBtn.classList.add('btn-disabled');
                this.createRoomBtn.title = '请先登录以创建房间';
            } else {
                this.userName.textContent = this.currentUser.username || this.currentUser.display_name;
                this.authButtons.classList.add('hidden');
                this.userInfo.classList.remove('hidden');
                // 已登录用户启用创建房间按钮
                this.createRoomBtn.classList.remove('btn-disabled');
                this.createRoomBtn.title = '创建新房间';
            }
        } else {
            // 未认证用户禁用创建房间按钮
            this.createRoomBtn.classList.add('btn-disabled');
            this.createRoomBtn.title = '请先登录以创建房间';
        }
    }

    showLoginModal() {
        this.loginModal.classList.remove('hidden');
        this.loginModal.classList.add('flex');
    }

    hideLoginModal() {
        this.loginModal.classList.add('hidden');
        this.loginModal.classList.remove('flex');
    }

    showRegisterModal() {
        this.registerModal.classList.remove('hidden');
        this.registerModal.classList.add('flex');
    }

    hideRegisterModal() {
        this.registerModal.classList.add('hidden');
        this.registerModal.classList.remove('flex');
    }

    async handleLogin(e) {
        e.preventDefault();
        const username = document.getElementById('login-username').value;
        const password = document.getElementById('login-password').value;

        try {
            const response = await fetch('/api/auth/login', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ username, password })
            });

            if (response.ok) {
                const data = await response.json();
                this.token = data.access_token;
                localStorage.setItem('access_token', this.token);
                this.currentUser = data.user;
                this.updateUI();
                this.hideLoginModal();
                this.loadRooms();
                this.showNotification('登录成功', 'success');
            } else {
                const error = await response.json();
                this.showNotification(error.detail || '登录失败', 'error');
            }
        } catch (error) {
            console.error('登录错误:', error);
            this.showNotification('登录失败，请重试', 'error');
        }
    }

    async handleRegister(e) {
        e.preventDefault();
        const username = document.getElementById('register-username').value;
        const password = document.getElementById('register-password').value;
        const confirmPassword = document.getElementById('register-confirm-password').value;

        // 验证密码确认
        if (password !== confirmPassword) {
            this.showNotification('两次输入的密码不一致', 'error');
            return;
        }

        try {
            const response = await fetch('/api/auth/register', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ username, password, confirm_password: confirmPassword })
            });

            if (response.ok) {
                const data = await response.json();
                this.token = data.access_token;
                localStorage.setItem('access_token', this.token);
                this.currentUser = data.user;
                this.updateUI();
                this.hideRegisterModal();
                this.loadRooms();
                this.showNotification('注册成功', 'success');
            } else {
                const error = await response.json();
                this.showNotification(error.detail || '注册失败', 'error');
            }
        } catch (error) {
            console.error('注册错误:', error);
            this.showNotification('注册失败，请重试', 'error');
        }
    }

    async logout() {
        try {
            // 调用后端登出API
            const response = await fetch('/api/auth/logout', {
                method: 'POST',
                headers: this.getAuthHeaders()
            });

            // 无论后端调用是否成功，都执行前端清理
            this.performLogoutCleanup();

            if (response.ok) {
                this.showNotification('已成功登出', 'success');
            } else {
                console.warn('后端登出API调用失败，但前端状态已清理');
                this.showNotification('已登出', 'info');
            }
        } catch (error) {
            console.error('登出过程中发生错误:', error);
            // 即使出错也要清理前端状态
            this.performLogoutCleanup();
            this.showNotification('已登出', 'info');
        }
    }

    performLogoutCleanup() {
        // 清除前端状态
        this.token = null;
        this.currentUser = null;
        this.currentRoom = null;

        // 清除localStorage
        localStorage.removeItem('access_token');

        // 清除所有可能的cookie
        this.clearAllCookies();

        // 重置UI状态
        this.resetUIToAnonymous();

        // 清空聊天区域
        this.showWelcomeScreen();

        // 重新检查认证状态（这会创建新的匿名用户）
        setTimeout(() => {
            this.checkAuthStatus();
        }, 100);
    }

    clearAllCookies() {
        // 获取所有可能的cookie路径和域名组合
        const paths = ['/', '/api', '/api/auth'];
        const domains = [window.location.hostname, '.' + window.location.hostname, 'localhost', '.localhost'];

        // 清除anonymous_id cookie的所有可能组合
        paths.forEach(path => {
            // 不指定域名
            document.cookie = `anonymous_id=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=${path};`;
            document.cookie = `anonymous_id=; max-age=0; path=${path};`;

            // 指定各种域名
            domains.forEach(domain => {
                document.cookie = `anonymous_id=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=${path}; domain=${domain};`;
                document.cookie = `anonymous_id=; max-age=0; path=${path}; domain=${domain};`;
            });
        });
    }

    resetUIToAnonymous() {
        // 显示登录/注册按钮，隐藏用户信息
        if (this.authButtons) {
            this.authButtons.classList.remove('hidden');
        }
        if (this.userInfo) {
            this.userInfo.classList.add('hidden');
        }

        // 禁用创建房间按钮
        if (this.createRoomBtn) {
            this.createRoomBtn.classList.add('btn-disabled');
            this.createRoomBtn.title = '请先登录以创建房间';
        }

        // 清空用户名显示
        if (this.userName) {
            this.userName.textContent = '';
        }
    }

    async loadRooms() {
        try {
            const response = await fetch('/api/rooms/', {
                headers: this.getAuthHeaders()
            });

            if (response.ok) {
                const rooms = await response.json();
                this.displayRooms(rooms);
            }
        } catch (error) {
            console.error('加载房间列表失败:', error);
        }
    }

    displayRooms(rooms) {
        this.roomList.innerHTML = '';

        rooms.forEach(room => {
            const roomElement = document.createElement('div');
            roomElement.className = 'p-3 bg-gray-50 rounded-lg cursor-pointer hover:bg-gray-100 transition-colors';
            roomElement.innerHTML = `
                <div class="font-medium text-gray-900">${room.name}</div>
                <div class="text-sm text-gray-500">${room.member_count || 0} 人</div>
            `;
            roomElement.addEventListener('click', () => this.joinRoom(room));
            this.roomList.appendChild(roomElement);
        });
    }

    showCreateRoomModal() {
        // 检查用户是否已登录且不是匿名用户
        if (!this.currentUser || this.currentUser.is_anonymous) {
            this.showNotification('请先登录以创建房间', 'warning');
            this.showLoginModal();
            return;
        }

        this.createRoomModal.classList.remove('hidden');
        this.createRoomModal.classList.add('flex');
        // 重置表单
        this.createRoomForm.reset();
        document.getElementById('room-max-members').value = '100';
        document.getElementById('room-type').value = 'private';
        this.togglePasswordField({ target: { value: 'private' } });
    }

    hideCreateRoomModal() {
        this.createRoomModal.classList.add('hidden');
        this.createRoomModal.classList.remove('flex');
    }

    togglePasswordField(e) {
        const passwordField = document.getElementById('password-field');
        if (e.target.value === 'password') {
            passwordField.classList.remove('hidden');
            document.getElementById('room-password').required = true;
        } else {
            passwordField.classList.add('hidden');
            document.getElementById('room-password').required = false;
        }
    }

    async handleCreateRoom(e) {
        e.preventDefault();

        const formData = new FormData(e.target);
        const roomData = {
            name: document.getElementById('room-name').value,
            description: document.getElementById('room-description').value || null,
            room_type: document.getElementById('room-type').value,
            max_members: parseInt(document.getElementById('room-max-members').value)
        };

        // 如果是密码房间，添加密码
        if (roomData.room_type === 'password') {
            roomData.password = document.getElementById('room-password').value;
        }

        try {
            const response = await fetch('/api/rooms/', {
                method: 'POST',
                headers: {
                    ...this.getAuthHeaders(),
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(roomData)
            });

            if (response.ok) {
                const room = await response.json();
                this.hideCreateRoomModal();
                this.loadRooms();
                this.showNotification('房间创建成功', 'success');
            } else {
                const error = await response.json();
                let errorMessage = '创建房间失败';

                if (error.detail) {
                    if (Array.isArray(error.detail)) {
                        // Pydantic验证错误
                        const validationErrors = error.detail.map(err => {
                            const field = err.loc ? err.loc.join('.') : '未知字段';
                            return `${field}: ${err.msg}`;
                        }).join(', ');
                        errorMessage = `验证错误: ${validationErrors}`;
                    } else if (typeof error.detail === 'string') {
                        errorMessage = error.detail;
                    }
                }

                this.showNotification(errorMessage, 'error');
            }
        } catch (error) {
            console.error('创建房间错误:', error);
            this.showNotification('创建房间失败，请重试', 'error');
        }
    }

    async joinRoom(room) {
        try {
            const response = await fetch(`/api/rooms/${room.id}/join`, {
                method: 'POST',
                headers: {
                    ...this.getAuthHeaders(),
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({}) // 发送空的请求体以满足API要求
            });

            if (response.ok) {
                this.currentRoom = room;
                this.showChatArea();
                this.loadMessages();
                this.connectWebSocket();
            } else {
                const error = await response.json();
                this.showNotification(error.detail || '加入房间失败', 'error');
            }
        } catch (error) {
            console.error('加入房间错误:', error);
            this.showNotification('加入房间失败，请重试', 'error');
        }
    }

    showWelcomeScreen() {
        this.welcomeScreen.classList.remove('hidden');
        this.chatArea.classList.add('hidden');
        this.chatHeader.classList.add('hidden');
    }

    showChatArea() {
        this.welcomeScreen.classList.add('hidden');
        this.chatArea.classList.remove('hidden');
        this.chatHeader.classList.remove('hidden');

        document.getElementById('room-name').textContent = this.currentRoom.name;
        document.getElementById('room-members').textContent = `${this.currentRoom.member_count || 0} 人在线`;
    }

    async loadMessages() {
        try {
            const response = await fetch(`/api/rooms/${this.currentRoom.id}/messages`, {
                headers: this.getAuthHeaders()
            });

            if (response.ok) {
                const messages = await response.json();
                this.displayMessages(messages);
            }
        } catch (error) {
            console.error('加载消息失败:', error);
        }
    }

    displayMessages(messages) {
        this.messagesContainer.innerHTML = '';

        messages.forEach(message => {
            this.addMessageToUI(message);
        });

        this.scrollToBottom();
    }

    addMessageToUI(message) {
        const messageElement = document.createElement('div');
        const isOwn = message.sender_id === this.currentUser.id;

        messageElement.className = `flex ${isOwn ? 'justify-end' : 'justify-start'}`;
        messageElement.innerHTML = `
            <div class="message-bubble ${isOwn ? 'own text-white' : 'other'} px-4 py-2 rounded-lg">
                ${!isOwn ? `<div class="text-xs text-gray-500 mb-1">${message.sender_name || '匿名用户'}</div>` : ''}
                <div>${message.content}</div>
                <div class="text-xs ${isOwn ? 'text-gray-200' : 'text-gray-400'} mt-1">
                    ${new Date(message.created_at).toLocaleTimeString()}
                </div>
            </div>
        `;

        this.messagesContainer.appendChild(messageElement);
    }

    async sendMessage() {
        const content = this.messageInput.value.trim();
        if (!content || !this.currentRoom) return;

        try {
            const response = await fetch(`/api/rooms/${this.currentRoom.id}/messages`, {
                method: 'POST',
                headers: this.getAuthHeaders(),
                body: JSON.stringify({ content, type: 'text' })
            });

            if (response.ok) {
                this.messageInput.value = '';
            } else {
                const error = await response.json();
                this.showNotification(error.detail || '发送消息失败', 'error');
            }
        } catch (error) {
            console.error('发送消息错误:', error);
            this.showNotification('发送消息失败，请重试', 'error');
        }
    }

    connectWebSocket() {
        if (this.socket) {
            this.socket.close();
        }

        const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        const wsUrl = `${protocol}//${window.location.host}/ws/${this.currentRoom.id}`;

        this.socket = new WebSocket(wsUrl);

        this.socket.onmessage = (event) => {
            const message = JSON.parse(event.data);
            this.addMessageToUI(message);
            this.scrollToBottom();
        };

        this.socket.onerror = (error) => {
            console.error('WebSocket错误:', error);
        };
    }

    scrollToBottom() {
        this.messagesContainer.scrollTop = this.messagesContainer.scrollHeight;
    }

    showNotification(message, type = 'info') {
        // 简单的通知实现
        const notification = document.createElement('div');
        notification.className = `fixed top-4 right-4 px-4 py-2 rounded-md text-white z-50 ${
            type === 'success' ? 'bg-green-500' :
            type === 'error' ? 'bg-red-500' :
            'bg-blue-500'
        }`;
        notification.textContent = message;

        document.body.appendChild(notification);

        setTimeout(() => {
            notification.remove();
        }, 3000);
    }
}

// 初始化应用
document.addEventListener('DOMContentLoaded', () => {
    new ChatApp();
});