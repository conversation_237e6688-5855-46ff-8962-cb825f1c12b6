# Web框架和服务器
fastapi==0.104.1
uvicorn[standard]==0.24.0
websockets==12.0

# 数据库
sqlalchemy==2.0.23
alembic==1.12.1
aiosqlite==0.19.0  # SQLite异步支持
asyncpg==0.29.0  # PostgreSQL异步支持
psycopg2-binary==2.9.9  # PostgreSQL支持

# 认证和安全
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6

# 文件处理
Pillow==10.1.0
python-magic==0.4.27  # 文件类型检测

# 数据验证
pydantic==2.5.0
pydantic-settings==2.1.0
email-validator==2.1.0

# 工具库
python-dotenv==1.0.0
aiofiles==23.2.1
jinja2==3.1.2

# 开发工具
pytest==7.4.3
pytest-asyncio==0.21.1
black==23.11.0
flake8==6.1.0

# 其他依赖
redis==5.0.1  # 用于会话存储和缓存
celery==5.3.4  # 异步任务处理