"""
认证中间件
"""
from fastapi import Request, HTTPException, status
from fastapi.responses import RedirectResponse
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import Response
import jwt
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from app.core.config import settings
from app.core.database import get_db
from app.models.user import User, UserRole


class AdminAuthMiddleware(BaseHTTPMiddleware):
    """管理员认证中间件"""
    
    def __init__(self, app):
        super().__init__(app)
        self.protected_paths = ['/admin']  # 需要管理员权限的路径
    
    async def dispatch(self, request: Request, call_next):
        # 检查是否是需要保护的路径
        if not any(request.url.path.startswith(path) for path in self.protected_paths):
            return await call_next(request)
        
        # 检查是否是API请求
        if request.url.path.startswith('/api/'):
            # API请求由路由中的依赖项处理认证
            return await call_next(request)
        
        # 检查认证状态
        auth_result = await self.check_admin_auth(request)
        
        if not auth_result['authenticated']:
            # 未认证，重定向到登录页面
            redirect_url = f"/login?redirect={request.url.path}"
            return RedirectResponse(url=redirect_url, status_code=302)
        
        if not auth_result['is_admin']:
            # 不是管理员，重定向到首页
            return RedirectResponse(url="/", status_code=302)
        
        # 认证通过，继续处理请求
        return await call_next(request)
    
    async def check_admin_auth(self, request: Request) -> dict:
        """检查管理员认证状态"""
        try:
            # 从localStorage中获取token（通过cookie或header）
            auth_header = request.headers.get('Authorization')
            token = None
            
            if auth_header and auth_header.startswith('Bearer '):
                token = auth_header.split(' ')[1]
            else:
                # 尝试从cookie中获取（如果前端设置了）
                token = request.cookies.get('access_token')
            
            if not token:
                return {'authenticated': False, 'is_admin': False}
            
            # 验证JWT token
            try:
                payload = jwt.decode(
                    token, 
                    settings.JWT_SECRET_KEY, 
                    algorithms=[settings.JWT_ALGORITHM]
                )
                user_id = payload.get('sub')
                if not user_id:
                    return {'authenticated': False, 'is_admin': False}
            except jwt.PyJWTError:
                return {'authenticated': False, 'is_admin': False}
            
            # 查询用户信息
            async for db in get_db():
                try:
                    result = await db.execute(
                        select(User).where(User.id == user_id)
                    )
                    user = result.scalar_one_or_none()
                    
                    if not user or not user.is_active:
                        return {'authenticated': False, 'is_admin': False}
                    
                    is_admin = user.role == UserRole.ADMIN
                    return {
                        'authenticated': True, 
                        'is_admin': is_admin,
                        'user': user
                    }
                finally:
                    await db.close()
                    
        except Exception as e:
            print(f"认证检查错误: {e}")
            return {'authenticated': False, 'is_admin': False}
        
        return {'authenticated': False, 'is_admin': False}


def require_admin_page_auth():
    """页面级管理员认证装饰器"""
    def decorator(func):
        async def wrapper(request: Request, *args, **kwargs):
            # 这个装饰器主要用于模板路由
            # 实际的认证逻辑由中间件处理
            return await func(request, *args, **kwargs)
        return wrapper
    return decorator
