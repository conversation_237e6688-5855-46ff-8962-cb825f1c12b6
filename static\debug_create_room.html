<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>调试创建房间</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-md mx-auto bg-white rounded-lg shadow-md p-6">
        <h1 class="text-2xl font-bold mb-6">调试创建房间</h1>
        
        <!-- 登录表单 -->
        <div id="login-section" class="mb-6">
            <h2 class="text-lg font-semibold mb-4">1. 登录</h2>
            <form id="login-form" class="space-y-4">
                <input type="text" id="username" placeholder="用户名" value="admin" class="w-full p-2 border rounded">
                <input type="password" id="password" placeholder="密码" value="admin123" class="w-full p-2 border rounded">
                <button type="submit" class="w-full bg-blue-500 text-white p-2 rounded hover:bg-blue-600">登录</button>
            </form>
            <div id="login-result" class="mt-2 text-sm"></div>
        </div>

        <!-- 创建房间表单 -->
        <div id="create-room-section" class="mb-6">
            <h2 class="text-lg font-semibold mb-4">2. 创建房间</h2>
            <form id="create-room-form" class="space-y-4">
                <input type="text" id="room-name" placeholder="房间名称" value="调试测试房间" class="w-full p-2 border rounded" required>
                <textarea id="room-description" placeholder="房间描述" class="w-full p-2 border rounded">这是一个调试测试房间</textarea>
                <select id="room-type" class="w-full p-2 border rounded">
                    <option value="public">公开房间</option>
                    <option value="private">私有房间</option>
                    <option value="password">密码房间</option>
                </select>
                <input type="password" id="room-password" placeholder="房间密码（密码房间需要）" class="w-full p-2 border rounded hidden">
                <input type="number" id="room-max-members" placeholder="最大成员数" value="10" min="2" max="1000" class="w-full p-2 border rounded">
                <button type="submit" class="w-full bg-green-500 text-white p-2 rounded hover:bg-green-600">创建房间</button>
            </form>
            <div id="create-room-result" class="mt-2 text-sm"></div>
        </div>

        <!-- 调试日志 -->
        <div id="debug-section">
            <h2 class="text-lg font-semibold mb-4">3. 调试日志</h2>
            <div id="debug-log" class="bg-gray-100 p-4 rounded text-xs font-mono max-h-64 overflow-y-auto"></div>
        </div>
    </div>

    <script>
        let currentToken = null;

        function log(message) {
            const debugLog = document.getElementById('debug-log');
            const timestamp = new Date().toLocaleTimeString();
            debugLog.innerHTML += `[${timestamp}] ${message}\n`;
            debugLog.scrollTop = debugLog.scrollHeight;
            console.log(message);
        }

        function showResult(elementId, message, isError = false) {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `mt-2 text-sm ${isError ? 'text-red-600' : 'text-green-600'}`;
        }

        // 登录处理
        document.getElementById('login-form').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            log(`尝试登录: ${username}`);
            
            try {
                const response = await fetch('/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ username, password })
                });

                log(`登录响应状态: ${response.status}`);
                
                if (response.ok) {
                    const result = await response.json();
                    currentToken = result.access_token;
                    log(`登录成功，获得token: ${currentToken.substring(0, 20)}...`);
                    showResult('login-result', '登录成功！');
                } else {
                    const error = await response.json();
                    log(`登录失败: ${JSON.stringify(error)}`);
                    showResult('login-result', `登录失败: ${error.detail}`, true);
                }
            } catch (error) {
                log(`登录错误: ${error.message}`);
                showResult('login-result', `登录错误: ${error.message}`, true);
            }
        });

        // 房间类型变化处理
        document.getElementById('room-type').addEventListener('change', (e) => {
            const passwordField = document.getElementById('room-password');
            if (e.target.value === 'password') {
                passwordField.classList.remove('hidden');
                passwordField.required = true;
            } else {
                passwordField.classList.add('hidden');
                passwordField.required = false;
            }
        });

        // 创建房间处理
        document.getElementById('create-room-form').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            if (!currentToken) {
                showResult('create-room-result', '请先登录！', true);
                return;
            }

            // 获取表单数据
            const roomName = document.getElementById('room-name').value.trim();
            const roomDescription = document.getElementById('room-description').value.trim();
            const roomType = document.getElementById('room-type').value;
            const maxMembers = parseInt(document.getElementById('room-max-members').value);
            const roomPassword = document.getElementById('room-password').value;

            log(`准备创建房间:`);
            log(`- 房间名称: "${roomName}"`);
            log(`- 房间描述: "${roomDescription}"`);
            log(`- 房间类型: "${roomType}"`);
            log(`- 最大成员: ${maxMembers}`);
            log(`- 密码: "${roomPassword}"`);

            // 验证数据
            if (!roomName) {
                showResult('create-room-result', '房间名称不能为空！', true);
                return;
            }

            if (maxMembers < 2 || maxMembers > 1000) {
                showResult('create-room-result', '最大成员数必须在2-1000之间！', true);
                return;
            }

            if (roomType === 'password' && !roomPassword) {
                showResult('create-room-result', '密码房间必须设置密码！', true);
                return;
            }

            // 构建请求数据
            const roomData = {
                name: roomName,
                description: roomDescription || null,
                room_type: roomType,
                max_members: maxMembers
            };

            if (roomType === 'password') {
                roomData.password = roomPassword;
            }

            log(`发送请求数据: ${JSON.stringify(roomData, null, 2)}`);

            try {
                const response = await fetch('/api/rooms/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${currentToken}`
                    },
                    body: JSON.stringify(roomData)
                });

                log(`创建房间响应状态: ${response.status}`);
                
                if (response.ok) {
                    const room = await response.json();
                    log(`房间创建成功: ${JSON.stringify(room, null, 2)}`);
                    showResult('create-room-result', `房间创建成功: ${room.name}`);
                } else {
                    const error = await response.json();
                    log(`房间创建失败: ${JSON.stringify(error, null, 2)}`);
                    
                    let errorMessage = '创建房间失败';
                    if (error.detail) {
                        if (Array.isArray(error.detail)) {
                            // Pydantic验证错误
                            const validationErrors = error.detail.map(err => {
                                const field = err.loc ? err.loc.join('.') : '未知字段';
                                return `${field}: ${err.msg}`;
                            }).join(', ');
                            errorMessage = `验证错误: ${validationErrors}`;
                        } else if (typeof error.detail === 'string') {
                            errorMessage = error.detail;
                        }
                    }
                    
                    showResult('create-room-result', errorMessage, true);
                }
            } catch (error) {
                log(`创建房间错误: ${error.message}`);
                showResult('create-room-result', `创建房间错误: ${error.message}`, true);
            }
        });

        log('调试页面已加载');
    </script>
</body>
</html>
