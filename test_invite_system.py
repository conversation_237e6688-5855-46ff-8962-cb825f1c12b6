#!/usr/bin/env python3
"""
测试邀请链接系统
"""
import requests
import json
import time

BASE_URL = "http://localhost:8000"

def test_invite_system():
    """测试邀请链接系统"""
    print("🧪 开始测试邀请链接系统...")
    
    # 1. 管理员登录
    print("\n1. 管理员登录...")
    login_response = requests.post(f"{BASE_URL}/api/auth/login", json={
        "username": "admin",
        "password": "admin123"
    })
    
    if login_response.status_code != 200:
        print(f"❌ 管理员登录失败: {login_response.text}")
        return
    
    admin_token = login_response.json()["access_token"]
    admin_headers = {"Authorization": f"Bearer {admin_token}"}
    print("✅ 管理员登录成功")
    
    # 2. 创建房间
    print("\n2. 创建测试房间...")
    room_data = {
        "name": "邀请测试房间",
        "description": "用于测试邀请链接功能的房间",
        "room_type": "private",
        "max_members": 50
    }
    
    create_response = requests.post(f"{BASE_URL}/api/rooms/", 
                                  json=room_data, 
                                  headers=admin_headers)
    
    if create_response.status_code != 200:
        print(f"❌ 创建房间失败: {create_response.text}")
        return
    
    room_info = create_response.json()
    room_id = room_info["id"]
    invite_code = room_info.get("invite_code")
    
    print(f"✅ 房间创建成功")
    print(f"   房间ID: {room_id}")
    print(f"   邀请码: {invite_code}")
    
    # 3. 获取邀请链接
    print("\n3. 获取邀请链接...")
    invite_response = requests.get(f"{BASE_URL}/api/rooms/{room_id}/invite",
                                 headers=admin_headers)
    
    if invite_response.status_code != 200:
        print(f"❌ 获取邀请链接失败: {invite_response.text}")
        return
    
    invite_info = invite_response.json()
    print(f"✅ 邀请链接获取成功")
    print(f"   邀请码: {invite_info['invite_code']}")
    print(f"   邀请URL: {invite_info['invite_url']}")
    
    # 4. 创建普通用户
    print("\n4. 注册普通用户...")
    import random
    username = f"testuser{random.randint(1000, 9999)}"
    register_response = requests.post(f"{BASE_URL}/api/auth/register", json={
        "username": username,
        "password": "test123",
        "confirm_password": "test123"
    })
    
    if register_response.status_code != 200:
        print(f"❌ 用户注册失败: {register_response.text}")
        return
    
    print("✅ 普通用户注册成功")
    
    # 5. 普通用户登录
    print("\n5. 普通用户登录...")
    user_login_response = requests.post(f"{BASE_URL}/api/auth/login", json={
        "username": username,
        "password": "test123"
    })
    
    if user_login_response.status_code != 200:
        print(f"❌ 普通用户登录失败: {user_login_response.text}")
        return
    
    user_token = user_login_response.json()["access_token"]
    user_headers = {"Authorization": f"Bearer {user_token}"}
    print("✅ 普通用户登录成功")
    
    # 6. 通过邀请码加入房间
    print("\n6. 通过邀请码加入房间...")
    join_response = requests.post(f"{BASE_URL}/api/rooms/invite/{invite_code}/join",
                                json={"password": None},
                                headers=user_headers)
    
    if join_response.status_code != 200:
        print(f"❌ 通过邀请码加入房间失败: {join_response.text}")
        return
    
    join_result = join_response.json()
    print(f"✅ 通过邀请码加入房间成功")
    print(f"   消息: {join_result['message']}")
    print(f"   房间名: {join_result['room']['name']}")
    
    # 7. 测试发送消息（实时显示功能）
    print("\n7. 测试发送消息...")
    message_response = requests.post(f"{BASE_URL}/api/rooms/{room_id}/messages",
                                   json={"content": "测试消息：邀请链接功能正常！", "type": "text"},
                                   headers=user_headers)
    
    if message_response.status_code != 200:
        print(f"❌ 发送消息失败: {message_response.text}")
        return
    
    message_result = message_response.json()
    print(f"✅ 消息发送成功")
    print(f"   消息ID: {message_result['id']}")
    print(f"   消息内容: {message_result['content']}")
    
    # 8. 获取消息列表
    print("\n8. 获取消息列表...")
    messages_response = requests.get(f"{BASE_URL}/api/rooms/{room_id}/messages",
                                   headers=user_headers)
    
    if messages_response.status_code != 200:
        print(f"❌ 获取消息列表失败: {messages_response.text}")
        return
    
    messages = messages_response.json()
    print(f"✅ 消息列表获取成功，共 {len(messages)} 条消息")
    
    # 9. 测试普通用户无法获取邀请链接
    print("\n9. 测试普通用户权限...")
    user_invite_response = requests.get(f"{BASE_URL}/api/rooms/{room_id}/invite",
                                      headers=user_headers)
    
    if user_invite_response.status_code == 403:
        print("✅ 普通用户无法获取邀请链接（权限控制正常）")
    else:
        print(f"❌ 权限控制异常: {user_invite_response.status_code}")
    
    print("\n🎉 邀请链接系统测试完成！")
    print("\n📋 测试总结:")
    print("✅ 房间创建时自动生成邀请码")
    print("✅ 房主可以获取邀请链接")
    print("✅ 用户可以通过邀请码加入房间")
    print("✅ 消息发送和接收功能正常")
    print("✅ 权限控制正常（普通用户无法获取邀请链接）")

def test_password_room_invite():
    """测试密码房间的邀请功能"""
    print("\n🔐 测试密码房间邀请功能...")
    
    # 管理员登录
    login_response = requests.post(f"{BASE_URL}/api/auth/login", json={
        "username": "admin",
        "password": "admin123"
    })
    
    admin_token = login_response.json()["access_token"]
    admin_headers = {"Authorization": f"Bearer {admin_token}"}
    
    # 创建密码房间
    room_data = {
        "name": "密码测试房间",
        "description": "需要密码的测试房间",
        "room_type": "password",
        "password": "secret123",
        "max_members": 20
    }
    
    create_response = requests.post(f"{BASE_URL}/api/rooms/", 
                                  json=room_data, 
                                  headers=admin_headers)
    
    if create_response.status_code != 200:
        print(f"❌ 创建密码房间失败: {create_response.text}")
        return
    
    room_info = create_response.json()
    invite_code = room_info.get("invite_code")
    print(f"✅ 密码房间创建成功，邀请码: {invite_code}")
    
    # 普通用户登录（使用已存在的用户）
    user_login_response = requests.post(f"{BASE_URL}/api/auth/login", json={
        "username": "testuser",
        "password": "test123"
    })
    
    user_token = user_login_response.json()["access_token"]
    user_headers = {"Authorization": f"Bearer {user_token}"}
    
    # 测试无密码加入（应该失败）
    join_response = requests.post(f"{BASE_URL}/api/rooms/invite/{invite_code}/join",
                                json={"password": None},
                                headers=user_headers)
    
    if join_response.status_code == 400:
        print("✅ 无密码加入密码房间被正确拒绝")
    else:
        print(f"❌ 密码验证异常: {join_response.status_code}")
    
    # 测试错误密码（应该失败）
    join_response = requests.post(f"{BASE_URL}/api/rooms/invite/{invite_code}/join",
                                json={"password": "wrongpassword"},
                                headers=user_headers)
    
    if join_response.status_code == 401:
        print("✅ 错误密码被正确拒绝")
    else:
        print(f"❌ 密码验证异常: {join_response.status_code}")
    
    # 测试正确密码（应该成功）
    join_response = requests.post(f"{BASE_URL}/api/rooms/invite/{invite_code}/join",
                                json={"password": "secret123"},
                                headers=user_headers)
    
    if join_response.status_code == 200:
        print("✅ 正确密码加入成功")
    else:
        print(f"❌ 正确密码加入失败: {join_response.text}")

if __name__ == "__main__":
    try:
        test_invite_system()
        test_password_room_invite()
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
