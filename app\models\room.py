"""
房间模型
"""
import uuid
from datetime import datetime, timezone
from sqlalchemy import Column, String, Boolean, DateTime, Text, Enum, ForeignKey, Integer
from sqlalchemy.orm import relationship
import enum

from app.core.database import Base


class RoomType(enum.Enum):
    """房间类型枚举"""
    PUBLIC = "public"      # 公开房间
    PRIVATE = "private"    # 私有房间
    PASSWORD = "password"  # 密码房间


class RoomStatus(enum.Enum):
    """房间状态枚举"""
    ACTIVE = "active"
    INACTIVE = "inactive"
    ARCHIVED = "archived"


class MemberRole(enum.Enum):
    """成员角色枚举"""
    OWNER = "owner"
    ADMIN = "admin"
    MEMBER = "member"


class Room(Base):
    """房间模型"""
    __tablename__ = "rooms"

    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    name = Column(String(100), nullable=False)
    description = Column(Text, nullable=True)

    # 房间类型和访问控制
    room_type = Column(Enum(RoomType), default=RoomType.PRIVATE)
    password_hash = Column(String(255), nullable=True)  # 密码房间的密码哈希

    # 邀请链接
    invite_code = Column(String(32), unique=True, nullable=True, index=True)
    invite_expires_at = Column(DateTime, nullable=True)

    # 房间设置
    max_members = Column(Integer, default=100)
    is_active = Column(Boolean, default=True)
    status = Column(Enum(RoomStatus), default=RoomStatus.ACTIVE)

    # 创建者
    creator_id = Column(String(36), ForeignKey("users.id"), nullable=False)

    # 时间戳
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    updated_at = Column(DateTime, default=lambda: datetime.now(timezone.utc),
                       onupdate=lambda: datetime.now(timezone.utc))

    # 关系
    creator = relationship("User", back_populates="created_rooms", foreign_keys=[creator_id])
    members = relationship("RoomMember", back_populates="room", cascade="all, delete-orphan")
    messages = relationship("Message", back_populates="room", cascade="all, delete-orphan")

    def __repr__(self):
        return f"<Room {self.name}>"

    @property
    def member_count(self):
        """获取房间成员数量"""
        return len(self.members)

    @property
    def is_full(self):
        """检查房间是否已满"""
        return self.member_count >= self.max_members

    def can_join(self, user, password=None):
        """检查用户是否可以加入房间"""
        if not self.is_active:
            return False, "房间已关闭"

        if self.is_full:
            return False, "房间已满"

        # 检查是否已经是成员
        if any(member.user_id == user.id for member in self.members):
            return False, "已经是房间成员"

        # 检查房间类型
        if self.room_type == RoomType.PUBLIC:
            return True, "可以加入"
        elif self.room_type == RoomType.PASSWORD:
            if not password:
                return False, "需要密码"
            # 这里需要验证密码
            from passlib.context import CryptContext
            pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
            if not pwd_context.verify(password, self.password_hash):
                return False, "密码错误"
            return True, "可以加入"
        else:  # PRIVATE
            return False, "私有房间需要邀请"


class RoomMember(Base):
    """房间成员模型"""
    __tablename__ = "room_members"

    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    room_id = Column(String(36), ForeignKey("rooms.id"), nullable=False)
    user_id = Column(String(36), ForeignKey("users.id"), nullable=False)

    # 成员角色和状态
    role = Column(Enum(MemberRole), default=MemberRole.MEMBER)
    is_active = Column(Boolean, default=True)

    # 时间戳
    joined_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    left_at = Column(DateTime, nullable=True)

    # 关系
    room = relationship("Room", back_populates="members")
    user = relationship("User", back_populates="room_memberships")

    def __repr__(self):
        return f"<RoomMember {self.user_id} in {self.room_id}>"