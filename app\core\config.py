"""
应用配置管理
"""
import os
from typing import List
from pydantic_settings import BaseSettings
from pydantic import field_validator


class Settings(BaseSettings):
    """应用设置"""

    # 应用基础配置
    APP_NAME: str = "聊天平台"
    APP_VERSION: str = "1.0.0"
    DEBUG: bool = True
    SECRET_KEY: str = "your-secret-key-change-this-in-production"

    # 数据库配置
    DATABASE_URL: str = "sqlite:///./chat_platform.db"

    # Redis配置
    REDIS_URL: str = "redis://localhost:6379/0"

    # 文件上传配置
    MAX_FILE_SIZE: int = 5242880  # 5MB
    UPLOAD_PATH: str = "./uploads"
    ALLOWED_IMAGE_EXTENSIONS: str = "jpg,jpeg,png,gif,webp"
    ALLOWED_AUDIO_EXTENSIONS: str = "mp3,wav,ogg,m4a"

    # JWT配置
    JWT_SECRET_KEY: str = "your-jwt-secret-key-change-this"
    JWT_ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30

    # 管理员配置
    ADMIN_USERNAME: str = "admin"
    ADMIN_PASSWORD: str = "admin123"

    # 聊天配置
    MESSAGE_RECALL_TIME_LIMIT: int = 300  # 5分钟
    CHAT_HISTORY_PAGE_SIZE: int = 10
    MAX_ROOM_MEMBERS: int = 100

    # 邮件配置
    SMTP_SERVER: str = "smtp.gmail.com"
    SMTP_PORT: int = 587
    SMTP_USERNAME: str = ""
    SMTP_PASSWORD: str = ""

    def get_allowed_image_extensions(self) -> List[str]:
        """获取允许的图片扩展名列表"""
        return [ext.strip() for ext in self.ALLOWED_IMAGE_EXTENSIONS.split(",")]

    def get_allowed_audio_extensions(self) -> List[str]:
        """获取允许的音频扩展名列表"""
        return [ext.strip() for ext in self.ALLOWED_AUDIO_EXTENSIONS.split(",")]

    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"


# 创建全局设置实例
settings = Settings()