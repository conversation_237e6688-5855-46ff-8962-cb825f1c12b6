"""
房间管理路由
"""
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_
from pydantic import BaseModel
from datetime import datetime, timezone

from app.core.database import get_db
from app.models.user import User
from app.models.room import Room, RoomMember, RoomType, RoomStatus, MemberRole
from app.routers.auth import get_current_user

router = APIRouter()


# Pydantic模型
class RoomCreateRequest(BaseModel):
    name: str
    description: Optional[str] = None
    room_type: str = "private"  # public, private, password
    password: Optional[str] = None
    max_members: int = 100


class RoomResponse(BaseModel):
    id: str
    name: str
    description: Optional[str]
    room_type: str
    max_members: int
    member_count: int
    is_active: bool
    creator_username: Optional[str]
    created_at: datetime
    is_member: bool = False
    invite_code: Optional[str] = None  # 只有房主或管理员能看到


class RoomJoinRequest(BaseModel):
    password: Optional[str] = None


class InviteJoinRequest(BaseModel):
    password: Optional[str] = None


@router.get("/", response_model=List[RoomResponse])
async def get_rooms(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
    page: int = Query(1, ge=1),
    size: int = Query(20, ge=1, le=100)
):
    """获取房间列表"""
    # 只显示公开房间和用户已加入的房间
    query = select(Room).where(
        and_(
            Room.is_active == True,
            Room.status == RoomStatus.ACTIVE
        )
    )

    # 分页
    offset = (page - 1) * size
    query = query.offset(offset).limit(size).order_by(Room.created_at.desc())

    result = await db.execute(query)
    rooms = result.scalars().all()

    # 构建响应
    room_responses = []
    for room in rooms:
        # 获取成员数量
        member_count = await db.scalar(
            select(func.count(RoomMember.id)).where(
                and_(
                    RoomMember.room_id == room.id,
                    RoomMember.is_active == True
                )
            )
        )

        # 获取创建者用户名
        creator_result = await db.execute(
            select(User.username).where(User.id == room.creator_id)
        )
        creator_username = creator_result.scalar_one_or_none()

        # 检查当前用户是否是成员
        is_member = False
        if not current_user.is_anonymous:
            member_result = await db.execute(
                select(RoomMember).where(
                    and_(
                        RoomMember.room_id == room.id,
                        RoomMember.user_id == current_user.id,
                        RoomMember.is_active == True
                    )
                )
            )
            is_member = member_result.scalar_one_or_none() is not None

        # 只显示公开房间或用户已加入的房间
        if room.room_type == RoomType.PUBLIC or is_member:
            room_responses.append(RoomResponse(
                id=str(room.id),
                name=room.name,
                description=room.description,
                room_type=room.room_type.value,
                max_members=room.max_members,
                member_count=member_count or 0,
                is_active=room.is_active,
                creator_username=creator_username,
                created_at=room.created_at,
                is_member=is_member
            ))

    return room_responses


@router.post("/", response_model=RoomResponse)
async def create_room(
    room_data: RoomCreateRequest,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """创建房间"""
    # 检查用户是否有创建房间的权限
    if current_user.is_anonymous:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="需要登录才能创建房间"
        )

    if not current_user.can_create_room:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有创建房间的权限"
        )

    # 验证房间类型
    try:
        room_type = RoomType(room_data.room_type)
    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="无效的房间类型"
        )

    # 生成邀请码
    import secrets
    invite_code = secrets.token_urlsafe(16)

    # 创建房间
    room = Room(
        name=room_data.name,
        description=room_data.description,
        room_type=room_type,
        max_members=room_data.max_members,
        creator_id=current_user.id,
        invite_code=invite_code
    )

    # 如果是密码房间，设置密码
    if room_type == RoomType.PASSWORD and room_data.password:
        from passlib.context import CryptContext
        pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
        room.password_hash = pwd_context.hash(room_data.password)

    db.add(room)
    await db.commit()
    await db.refresh(room)

    # 创建者自动加入房间
    room_member = RoomMember(
        room_id=room.id,
        user_id=current_user.id,
        role=MemberRole.OWNER
    )
    db.add(room_member)
    await db.commit()

    return RoomResponse(
        id=str(room.id),
        name=room.name,
        description=room.description,
        room_type=room.room_type.value,
        max_members=room.max_members,
        member_count=1,
        is_active=room.is_active,
        creator_username=current_user.username,
        created_at=room.created_at,
        is_member=True,
        invite_code=room.invite_code  # 创建者可以看到邀请码
    )


@router.post("/{room_id}/join")
async def join_room(
    room_id: str,
    join_data: RoomJoinRequest,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """加入房间"""
    if current_user.is_anonymous:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="需要登录才能加入房间"
        )

    # 获取房间
    result = await db.execute(select(Room).where(Room.id == room_id))
    room = result.scalar_one_or_none()

    if not room:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="房间不存在"
        )

    # 检查房间是否激活
    if not room.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="房间已关闭"
        )

    # 检查密码（如果是密码保护的房间）
    if room.room_type == RoomType.PASSWORD:
        if not join_data.password:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="需要提供房间密码"
            )
        if not room.verify_password(join_data.password):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="房间密码错误"
            )

    # 检查是否已经是成员
    existing_member = await db.execute(
        select(RoomMember).where(
            RoomMember.room_id == room.id,
            RoomMember.user_id == current_user.id,
            RoomMember.is_active == True
        )
    )
    existing_member_obj = existing_member.scalar_one_or_none()
    if existing_member_obj:
        # 如果已经是房间成员，直接返回成功，允许重新进入
        # 获取当前房间成员数量
        member_count_result = await db.execute(
            select(func.count(RoomMember.id)).where(
                RoomMember.room_id == room.id,
                RoomMember.is_active == True
            )
        )
        member_count = member_count_result.scalar()

        return {
            "message": "成功进入房间",
            "room": {
                "id": str(room.id),
                "name": room.name,
                "description": room.description,
                "room_type": room.room_type.value,
                "member_count": member_count,
                "is_active": room.is_active,
                "created_at": room.created_at.isoformat(),
                "is_member": True
            }
        }

    # 检查房间是否已满
    member_count_result = await db.execute(
        select(func.count(RoomMember.id)).where(
            RoomMember.room_id == room.id,
            RoomMember.is_active == True
        )
    )
    member_count = member_count_result.scalar()
    if member_count >= room.max_members:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="房间已满"
        )

    # 加入房间
    room_member = RoomMember(
        room_id=room.id,
        user_id=current_user.id,
        role=MemberRole.MEMBER
    )
    db.add(room_member)
    await db.commit()

    return {"message": "成功加入房间"}


@router.post("/{room_id}/leave")
async def leave_room(
    room_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """离开房间"""
    if current_user.is_anonymous:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="需要登录才能离开房间"
        )

    # 获取房间成员记录
    result = await db.execute(
        select(RoomMember).where(
            and_(
                RoomMember.room_id == room_id,
                RoomMember.user_id == current_user.id,
                RoomMember.is_active == True
            )
        )
    )
    room_member = result.scalar_one_or_none()

    if not room_member:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="您不是该房间的成员"
        )

    # 离开房间
    room_member.is_active = False
    room_member.left_at = datetime.now(timezone.utc)
    await db.commit()

    return {"message": "成功离开房间"}


@router.post("/invite/{invite_code}/join")
async def join_room_by_invite(
    invite_code: str,
    join_data: InviteJoinRequest,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """通过邀请码加入房间"""
    if current_user.is_anonymous:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="需要登录才能加入房间"
        )

    # 查找房间
    result = await db.execute(
        select(Room).where(Room.invite_code == invite_code, Room.is_active == True)
    )
    room = result.scalar_one_or_none()

    if not room:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="邀请链接无效或已过期"
        )

    # 检查房间是否需要密码
    if room.room_type == RoomType.PASSWORD:
        if not join_data.password:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="此房间需要密码"
            )

        # 验证密码
        from passlib.context import CryptContext
        pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
        if not pwd_context.verify(join_data.password, room.password_hash):
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="密码错误"
            )

    # 检查用户是否已经是房间成员
    existing_member = await db.execute(
        select(RoomMember).where(
            RoomMember.room_id == room.id,
            RoomMember.user_id == current_user.id,
            RoomMember.is_active == True
        )
    )

    if existing_member.scalar_one_or_none():
        # 用户已经是成员，直接返回成功
        member_count = await db.execute(
            select(func.count(RoomMember.id)).where(
                RoomMember.room_id == room.id,
                RoomMember.is_active == True
            )
        )

        room_response = RoomResponse(
            id=str(room.id),
            name=room.name,
            description=room.description,
            room_type=room.room_type.value,
            max_members=room.max_members,
            member_count=member_count.scalar(),
            is_active=room.is_active,
            creator_username=None,
            created_at=room.created_at,
            is_member=True
        )

        return {
            "message": "成功进入房间",
            "room": room_response
        }

    # 检查房间是否已满
    current_member_count = await db.execute(
        select(func.count(RoomMember.id)).where(
            RoomMember.room_id == room.id,
            RoomMember.is_active == True
        )
    )

    if current_member_count.scalar() >= room.max_members:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="房间已满"
        )

    # 添加用户到房间
    room_member = RoomMember(
        room_id=room.id,
        user_id=current_user.id,
        role=MemberRole.MEMBER
    )
    db.add(room_member)
    await db.commit()

    # 获取更新后的成员数量
    member_count = await db.execute(
        select(func.count(RoomMember.id)).where(
            RoomMember.room_id == room.id,
            RoomMember.is_active == True
        )
    )

    room_response = RoomResponse(
        id=str(room.id),
        name=room.name,
        description=room.description,
        room_type=room.room_type.value,
        max_members=room.max_members,
        member_count=member_count.scalar(),
        is_active=room.is_active,
        creator_username=None,
        created_at=room.created_at,
        is_member=True
    )

    return {
        "message": "成功加入房间",
        "room": room_response
    }


@router.get("/{room_id}/invite")
async def get_room_invite(
    room_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """获取房间邀请链接（仅房主和管理员）"""
    if current_user.is_anonymous:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="需要登录才能获取邀请链接"
        )

    # 检查用户是否是房间的房主或管理员
    result = await db.execute(
        select(RoomMember).where(
            RoomMember.room_id == room_id,
            RoomMember.user_id == current_user.id,
            RoomMember.is_active == True,
            RoomMember.role.in_([MemberRole.OWNER, MemberRole.ADMIN])
        )
    )

    room_member = result.scalar_one_or_none()
    if not room_member:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="只有房主和管理员可以获取邀请链接"
        )

    # 获取房间信息
    room_result = await db.execute(
        select(Room).where(Room.id == room_id, Room.is_active == True)
    )
    room = room_result.scalar_one_or_none()

    if not room:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="房间不存在"
        )

    return {
        "invite_code": room.invite_code,
        "invite_url": f"/invite/{room.invite_code}",
        "room_name": room.name,
        "room_type": room.room_type.value
    }