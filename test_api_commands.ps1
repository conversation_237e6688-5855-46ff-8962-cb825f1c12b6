# PowerShell API 测试命令
# 用于测试聊天平台的API接口

Write-Host "=== 聊天平台 API 测试命令 ===" -ForegroundColor Green

Write-Host "`n1. 测试获取房间列表:" -ForegroundColor Yellow
Write-Host 'Invoke-WebRequest -Uri "http://localhost:8000/api/rooms/" -Method GET' -ForegroundColor Cyan

Write-Host "`n2. 测试用户登录:" -ForegroundColor Yellow
Write-Host 'Invoke-WebRequest -Uri "http://localhost:8000/api/auth/login" -Method POST -Headers @{"Content-Type"="application/json"} -Body ''{"username":"admin","password":"admin123"}''' -ForegroundColor Cyan

Write-Host "`n3. 测试创建房间 (需要先登录获取token):" -ForegroundColor Yellow
Write-Host '$token = "your_access_token_here"' -ForegroundColor Cyan
Write-Host 'Invoke-WebRequest -Uri "http://localhost:8000/api/rooms/" -Method POST -Headers @{"Content-Type"="application/json";"Authorization"="Bearer $token"} -Body ''{"name":"测试房间","description":"API测试房间","room_type":"public","max_members":10}''' -ForegroundColor Cyan

Write-Host "`n4. 测试加入房间 (需要房间ID和token):" -ForegroundColor Yellow
Write-Host '$roomId = "room_id_here"' -ForegroundColor Cyan
Write-Host 'Invoke-WebRequest -Uri "http://localhost:8000/api/rooms/$roomId/join" -Method POST -Headers @{"Content-Type"="application/json";"Authorization"="Bearer $token"} -Body ''{}''' -ForegroundColor Cyan

Write-Host "`n5. 测试获取当前用户信息:" -ForegroundColor Yellow
Write-Host 'Invoke-WebRequest -Uri "http://localhost:8000/api/auth/me" -Method GET -Headers @{"Authorization"="Bearer $token"}' -ForegroundColor Cyan

Write-Host "`n=== 实际执行测试 ===" -ForegroundColor Green

# 1. 测试获取房间列表
Write-Host "`n正在测试获取房间列表..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "http://localhost:8000/api/rooms/" -Method GET
    Write-Host "✅ 房间列表获取成功 (状态码: $($response.StatusCode))" -ForegroundColor Green
    $rooms = $response.Content | ConvertFrom-Json
    Write-Host "房间数量: $($rooms.Count)" -ForegroundColor Cyan
} catch {
    Write-Host "❌ 房间列表获取失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 2. 测试用户登录
Write-Host "`n正在测试用户登录..." -ForegroundColor Yellow
try {
    $loginBody = @{
        username = "admin"
        password = "admin123"
    } | ConvertTo-Json
    
    $response = Invoke-WebRequest -Uri "http://localhost:8000/api/auth/login" -Method POST -Headers @{"Content-Type"="application/json"} -Body $loginBody
    Write-Host "✅ 用户登录成功 (状态码: $($response.StatusCode))" -ForegroundColor Green
    
    $loginResult = $response.Content | ConvertFrom-Json
    $global:token = $loginResult.access_token
    Write-Host "用户名: $($loginResult.user.username)" -ForegroundColor Cyan
    Write-Host "角色: $($loginResult.user.role)" -ForegroundColor Cyan
    Write-Host "Token已保存到变量 `$global:token" -ForegroundColor Cyan
} catch {
    Write-Host "❌ 用户登录失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 3. 测试创建房间
if ($global:token) {
    Write-Host "`n正在测试创建房间..." -ForegroundColor Yellow
    try {
        $roomBody = @{
            name = "PowerShell测试房间_$(Get-Date -Format 'HHmmss')"
            description = "通过PowerShell API测试创建的房间"
            room_type = "public"
            max_members = 10
        } | ConvertTo-Json

        $response = Invoke-WebRequest -Uri "http://localhost:8000/api/rooms/" -Method POST -Headers @{"Content-Type"="application/json";"Authorization"="Bearer $global:token"} -Body $roomBody
        Write-Host "✅ 房间创建成功 (状态码: $($response.StatusCode))" -ForegroundColor Green

        $newRoom = $response.Content | ConvertFrom-Json
        $global:testRoomId = $newRoom.id
        Write-Host "房间名称: $($newRoom.name)" -ForegroundColor Cyan
        Write-Host "房间ID: $($newRoom.id)" -ForegroundColor Cyan
        Write-Host "房间ID已保存到变量 `$global:testRoomId" -ForegroundColor Cyan
    } catch {
        Write-Host "❌ 房间创建失败: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# 4. 测试加入房间
if ($global:token -and $global:testRoomId) {
    Write-Host "`n正在测试加入房间..." -ForegroundColor Yellow
    try {
        $response = Invoke-WebRequest -Uri "http://localhost:8000/api/rooms/$global:testRoomId/join" -Method POST -Headers @{"Content-Type"="application/json";"Authorization"="Bearer $global:token"} -Body '{}'
        Write-Host "✅ 房间加入成功 (状态码: $($response.StatusCode))" -ForegroundColor Green
        
        $joinResult = $response.Content | ConvertFrom-Json
        Write-Host "消息: $($joinResult.message)" -ForegroundColor Cyan
    } catch {
        Write-Host "❌ 房间加入失败: $($_.Exception.Message)" -ForegroundColor Red
        Write-Host "错误详情: $($_.ErrorDetails.Message)" -ForegroundColor Red
    }
}

# 5. 测试获取当前用户信息
if ($global:token) {
    Write-Host "`n正在测试获取当前用户信息..." -ForegroundColor Yellow
    try {
        $response = Invoke-WebRequest -Uri "http://localhost:8000/api/auth/me" -Method GET -Headers @{"Authorization"="Bearer $global:token"}
        Write-Host "✅ 用户信息获取成功 (状态码: $($response.StatusCode))" -ForegroundColor Green
        
        $userInfo = $response.Content | ConvertFrom-Json
        Write-Host "用户ID: $($userInfo.id)" -ForegroundColor Cyan
        Write-Host "用户名: $($userInfo.username)" -ForegroundColor Cyan
        Write-Host "角色: $($userInfo.role)" -ForegroundColor Cyan
        Write-Host "是否可创建房间: $($userInfo.can_create_room)" -ForegroundColor Cyan
    } catch {
        Write-Host "❌ 用户信息获取失败: $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host "`n=== 测试完成 ===" -ForegroundColor Green
Write-Host "如果需要手动执行单个命令，请复制上面显示的命令。" -ForegroundColor Yellow
Write-Host "Token变量: `$global:token" -ForegroundColor Yellow
Write-Host "测试房间ID变量: `$global:testRoomId" -ForegroundColor Yellow
