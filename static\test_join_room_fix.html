<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试加入房间修复</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-2xl mx-auto bg-white rounded-lg shadow-lg p-6">
        <h1 class="text-2xl font-bold mb-6 text-center">测试加入房间修复</h1>
        
        <!-- 登录区域 -->
        <div class="mb-6 p-4 border rounded-lg">
            <h2 class="text-lg font-semibold mb-3">1. 登录测试</h2>
            <div class="flex gap-2">
                <input type="text" id="username" placeholder="用户名" value="admin" class="flex-1 px-3 py-2 border rounded">
                <input type="password" id="password" placeholder="密码" value="admin123" class="flex-1 px-3 py-2 border rounded">
                <button onclick="login()" class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600">登录</button>
            </div>
            <div id="login-result" class="mt-2 text-sm"></div>
        </div>

        <!-- 房间列表区域 -->
        <div class="mb-6 p-4 border rounded-lg">
            <h2 class="text-lg font-semibold mb-3">2. 获取房间列表</h2>
            <button onclick="getRooms()" class="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600">获取房间列表</button>
            <div id="rooms-result" class="mt-2"></div>
        </div>

        <!-- 加入房间测试区域 -->
        <div class="mb-6 p-4 border rounded-lg">
            <h2 class="text-lg font-semibold mb-3">3. 加入房间测试</h2>
            <div class="flex gap-2 mb-3">
                <input type="text" id="room-id" placeholder="房间ID" class="flex-1 px-3 py-2 border rounded">
                <button onclick="joinRoom()" class="px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600">加入房间</button>
            </div>
            <div id="join-result" class="mt-2 text-sm"></div>
        </div>

        <!-- 日志区域 -->
        <div class="p-4 border rounded-lg">
            <h2 class="text-lg font-semibold mb-3">测试日志</h2>
            <div id="log" class="bg-gray-50 p-3 rounded text-sm font-mono max-h-60 overflow-y-auto"></div>
        </div>
    </div>

    <script>
        let currentToken = null;
        let currentUser = null;

        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const color = type === 'error' ? 'text-red-600' : type === 'success' ? 'text-green-600' : 'text-blue-600';
            logDiv.innerHTML += `<div class="${color}">[${timestamp}] ${message}</div>`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function showResult(elementId, message, isError = false) {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="p-2 rounded ${isError ? 'bg-red-100 text-red-700' : 'bg-green-100 text-green-700'}">${message}</div>`;
        }

        async function login() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;

            try {
                log(`尝试登录: ${username}`);
                const response = await fetch('/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ username, password })
                });

                const data = await response.json();
                
                if (response.ok) {
                    currentToken = data.access_token;
                    currentUser = data.user;
                    log(`登录成功: ${data.user.username} (${data.user.role})`, 'success');
                    showResult('login-result', `登录成功: ${data.user.username}`, false);
                } else {
                    log(`登录失败: ${data.detail}`, 'error');
                    showResult('login-result', `登录失败: ${data.detail}`, true);
                }
            } catch (error) {
                log(`登录错误: ${error.message}`, 'error');
                showResult('login-result', `登录错误: ${error.message}`, true);
            }
        }

        async function getRooms() {
            if (!currentToken) {
                showResult('rooms-result', '请先登录', true);
                return;
            }

            try {
                log('获取房间列表...');
                const response = await fetch('/api/rooms/', {
                    headers: {
                        'Authorization': `Bearer ${currentToken}`
                    }
                });

                const data = await response.json();
                
                if (response.ok) {
                    log(`获取到 ${data.length} 个房间`, 'success');
                    let roomsHtml = '<div class="mt-2">';
                    data.forEach(room => {
                        roomsHtml += `
                            <div class="p-2 border rounded mb-2 flex justify-between items-center">
                                <div>
                                    <strong>${room.name}</strong> (${room.member_count} 人)
                                    <br><small class="text-gray-500">ID: ${room.id}</small>
                                </div>
                                <button onclick="document.getElementById('room-id').value='${room.id}'" 
                                        class="px-2 py-1 bg-gray-200 text-sm rounded">选择</button>
                            </div>
                        `;
                    });
                    roomsHtml += '</div>';
                    showResult('rooms-result', roomsHtml, false);
                } else {
                    log(`获取房间失败: ${data.detail}`, 'error');
                    showResult('rooms-result', `获取房间失败: ${data.detail}`, true);
                }
            } catch (error) {
                log(`获取房间错误: ${error.message}`, 'error');
                showResult('rooms-result', `获取房间错误: ${error.message}`, true);
            }
        }

        async function joinRoom() {
            const roomId = document.getElementById('room-id').value;
            
            if (!currentToken) {
                showResult('join-result', '请先登录', true);
                return;
            }

            if (!roomId) {
                showResult('join-result', '请输入房间ID', true);
                return;
            }

            try {
                log(`尝试加入房间: ${roomId}`);
                const response = await fetch(`/api/rooms/${roomId}/join`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${currentToken}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({})
                });

                const data = await response.json();
                
                if (response.ok) {
                    log(`加入房间成功: ${data.message || '成功'}`, 'success');
                    let resultHtml = `<strong>加入成功!</strong><br>`;
                    if (data.room) {
                        resultHtml += `房间: ${data.room.name}<br>`;
                        resultHtml += `成员数: ${data.room.member_count}<br>`;
                        resultHtml += `类型: ${data.room.room_type}`;
                    }
                    showResult('join-result', resultHtml, false);
                } else {
                    log(`加入房间失败: ${data.detail}`, 'error');
                    showResult('join-result', `加入房间失败: ${data.detail}`, true);
                }
            } catch (error) {
                log(`加入房间错误: ${error.message}`, 'error');
                showResult('join-result', `加入房间错误: ${error.message}`, true);
            }
        }

        // 页面加载时的初始化
        log('页面加载完成，可以开始测试');
    </script>
</body>
</html>
