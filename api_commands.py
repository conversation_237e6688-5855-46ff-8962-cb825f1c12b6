#!/usr/bin/env python3
"""
聊天平台 API 命令行工具
提供简单的命令来测试各个API端点
"""

import requests
import json
import sys
import argparse
from typing import Optional

BASE_URL = "http://localhost:8000"

def make_request(method: str, endpoint: str, data: Optional[dict] = None, 
                token: Optional[str] = None) -> None:
    """发送HTTP请求并打印结果"""
    url = f"{BASE_URL}{endpoint}"
    
    headers = {"Content-Type": "application/json"}
    if token:
        headers["Authorization"] = f"Bearer {token}"
    
    try:
        if method.upper() == "GET":
            response = requests.get(url, headers=headers)
        elif method.upper() == "POST":
            json_data = json.dumps(data) if data else "{}"
            response = requests.post(url, data=json_data, headers=headers)
        else:
            print(f"❌ 不支持的方法: {method}")
            return
            
        print(f"📡 {method.upper()} {url}")
        print(f"📤 请求头: {json.dumps(headers, indent=2, ensure_ascii=False)}")
        if data:
            print(f"📤 请求体: {json.dumps(data, indent=2, ensure_ascii=False)}")
        
        print(f"📥 状态码: {response.status_code}")
        
        try:
            result = response.json()
            print(f"📥 响应体: {json.dumps(result, indent=2, ensure_ascii=False)}")
        except:
            print(f"📥 响应体: {response.text}")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求失败: {e}")

def cmd_get_rooms():
    """获取房间列表"""
    print("🏠 获取房间列表")
    make_request("GET", "/api/rooms/")

def cmd_login(username: str = "admin", password: str = "admin123"):
    """用户登录"""
    print(f"🔐 用户登录 (用户名: {username})")
    data = {"username": username, "password": password}
    make_request("POST", "/api/auth/login", data)

def cmd_create_room(token: str, name: str = "测试房间", room_type: str = "public", 
                   max_members: int = 10, description: str = "API测试房间"):
    """创建房间"""
    print(f"🏗️  创建房间 (名称: {name})")
    data = {
        "name": name,
        "description": description,
        "room_type": room_type,
        "max_members": max_members
    }
    make_request("POST", "/api/rooms/", data, token)

def cmd_join_room(token: str, room_id: str):
    """加入房间"""
    print(f"🚪 加入房间 (ID: {room_id})")
    make_request("POST", f"/api/rooms/{room_id}/join", {}, token)

def cmd_get_user_info(token: str):
    """获取当前用户信息"""
    print("👤 获取当前用户信息")
    make_request("GET", "/api/auth/me", token=token)

def print_help():
    """打印帮助信息"""
    help_text = """
🚀 聊天平台 API 命令行工具

使用方法:
  python api_commands.py <命令> [参数...]

可用命令:

1. 获取房间列表:
   python api_commands.py rooms

2. 用户登录:
   python api_commands.py login [用户名] [密码]
   例子: python api_commands.py login admin admin123

3. 创建房间:
   python api_commands.py create-room <token> [房间名] [类型] [最大成员数] [描述]
   例子: python api_commands.py create-room "your_token_here" "我的房间" public 20 "测试房间"

4. 加入房间:
   python api_commands.py join-room <token> <房间ID>
   例子: python api_commands.py join-room "your_token_here" "room-id-here"

5. 获取用户信息:
   python api_commands.py user-info <token>
   例子: python api_commands.py user-info "your_token_here"

6. 完整测试流程:
   python api_commands.py test-all

注意:
- 请确保服务器正在运行: python -m uvicorn main:app --reload --host 0.0.0.0 --port 8000
- token 可以通过登录命令获取
- 房间ID 可以通过获取房间列表或创建房间获取

示例完整流程:
1. python api_commands.py login admin admin123
2. 复制返回的 access_token
3. python api_commands.py create-room "your_token" "测试房间"
4. 复制返回的房间ID
5. python api_commands.py join-room "your_token" "room_id"
"""
    print(help_text)

def test_all():
    """运行完整的测试流程"""
    print("🧪 运行完整测试流程\n")
    
    # 1. 获取房间列表
    print("=" * 50)
    cmd_get_rooms()
    
    # 2. 登录
    print("\n" + "=" * 50)
    cmd_login()
    
    # 注意: 在实际使用中，你需要从登录响应中提取token
    print("\n⚠️  注意: 请从上面的登录响应中复制 access_token，然后手动运行后续命令")
    print("例如:")
    print('python api_commands.py create-room "your_token_here" "测试房间"')
    print('python api_commands.py join-room "your_token_here" "room_id_here"')
    print('python api_commands.py user-info "your_token_here"')

def main():
    """主函数"""
    if len(sys.argv) < 2:
        print_help()
        return
    
    command = sys.argv[1].lower()
    
    try:
        if command == "rooms":
            cmd_get_rooms()
            
        elif command == "login":
            username = sys.argv[2] if len(sys.argv) > 2 else "admin"
            password = sys.argv[3] if len(sys.argv) > 3 else "admin123"
            cmd_login(username, password)
            
        elif command == "create-room":
            if len(sys.argv) < 3:
                print("❌ 错误: 需要提供token")
                print("用法: python api_commands.py create-room <token> [房间名] [类型] [最大成员数] [描述]")
                return
            token = sys.argv[2]
            name = sys.argv[3] if len(sys.argv) > 3 else "测试房间"
            room_type = sys.argv[4] if len(sys.argv) > 4 else "public"
            max_members = int(sys.argv[5]) if len(sys.argv) > 5 else 10
            description = sys.argv[6] if len(sys.argv) > 6 else "API测试房间"
            cmd_create_room(token, name, room_type, max_members, description)
            
        elif command == "join-room":
            if len(sys.argv) < 4:
                print("❌ 错误: 需要提供token和房间ID")
                print("用法: python api_commands.py join-room <token> <房间ID>")
                return
            token = sys.argv[2]
            room_id = sys.argv[3]
            cmd_join_room(token, room_id)
            
        elif command == "user-info":
            if len(sys.argv) < 3:
                print("❌ 错误: 需要提供token")
                print("用法: python api_commands.py user-info <token>")
                return
            token = sys.argv[2]
            cmd_get_user_info(token)
            
        elif command == "test-all":
            test_all()
            
        elif command in ["help", "-h", "--help"]:
            print_help()
            
        else:
            print(f"❌ 未知命令: {command}")
            print_help()
            
    except KeyboardInterrupt:
        print("\n\n👋 测试被用户中断")
    except Exception as e:
        print(f"❌ 执行命令时出错: {e}")

if __name__ == "__main__":
    main()
