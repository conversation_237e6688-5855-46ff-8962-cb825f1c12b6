#!/usr/bin/env python3
"""
聊天平台 API 测试脚本
用Python替代PowerShell命令进行API测试
"""

import requests
import json
import sys
from datetime import datetime
from typing import Optional, Dict, Any

class ChatPlatformAPITester:
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.session = requests.Session()
        self.token: Optional[str] = None
        self.current_user: Optional[Dict] = None
        
    def print_success(self, message: str):
        """打印成功消息"""
        print(f"✅ {message}")
        
    def print_error(self, message: str):
        """打印错误消息"""
        print(f"❌ {message}")
        
    def print_info(self, message: str):
        """打印信息消息"""
        print(f"ℹ️  {message}")
        
    def print_section(self, title: str):
        """打印章节标题"""
        print(f"\n{'='*50}")
        print(f" {title}")
        print(f"{'='*50}")
        
    def make_request(self, method: str, endpoint: str, data: Optional[Dict] = None, 
                    headers: Optional[Dict] = None) -> Optional[requests.Response]:
        """发送HTTP请求"""
        url = f"{self.base_url}{endpoint}"
        
        # 默认headers
        default_headers = {"Content-Type": "application/json"}
        if self.token:
            default_headers["Authorization"] = f"Bearer {self.token}"
            
        # 合并headers
        if headers:
            default_headers.update(headers)
            
        try:
            if method.upper() == "GET":
                response = self.session.get(url, headers=default_headers)
            elif method.upper() == "POST":
                json_data = json.dumps(data) if data else "{}"
                response = self.session.post(url, data=json_data, headers=default_headers)
            elif method.upper() == "PUT":
                json_data = json.dumps(data) if data else "{}"
                response = self.session.put(url, data=json_data, headers=default_headers)
            elif method.upper() == "DELETE":
                response = self.session.delete(url, headers=default_headers)
            else:
                self.print_error(f"不支持的HTTP方法: {method}")
                return None
                
            return response
            
        except requests.exceptions.RequestException as e:
            self.print_error(f"请求失败: {e}")
            return None
    
    def test_get_rooms(self) -> bool:
        """测试获取房间列表"""
        print("\n1. 测试获取房间列表...")
        
        response = self.make_request("GET", "/api/rooms/")
        if not response:
            return False
            
        if response.status_code == 200:
            rooms = response.json()
            self.print_success(f"房间列表获取成功 (状态码: {response.status_code})")
            self.print_info(f"房间数量: {len(rooms)}")
            
            if rooms:
                print("房间列表:")
                for i, room in enumerate(rooms, 1):
                    print(f"  {i}. {room.get('name', 'N/A')} (ID: {room.get('id', 'N/A')})")
                    print(f"     类型: {room.get('room_type', 'N/A')}, 成员: {room.get('member_count', 0)}/{room.get('max_members', 'N/A')}")
            return True
        else:
            self.print_error(f"房间列表获取失败 (状态码: {response.status_code})")
            if response.text:
                print(f"错误详情: {response.text}")
            return False
    
    def test_login(self, username: str = "admin", password: str = "admin123") -> bool:
        """测试用户登录"""
        print(f"\n2. 测试用户登录 (用户名: {username})...")
        
        login_data = {
            "username": username,
            "password": password
        }
        
        response = self.make_request("POST", "/api/auth/login", login_data)
        if not response:
            return False
            
        if response.status_code == 200:
            result = response.json()
            self.token = result.get("access_token")
            self.current_user = result.get("user")
            
            self.print_success(f"用户登录成功 (状态码: {response.status_code})")
            self.print_info(f"用户名: {self.current_user.get('username', 'N/A')}")
            self.print_info(f"角色: {self.current_user.get('role', 'N/A')}")
            self.print_info(f"Token: {self.token[:20]}..." if self.token else "Token: None")
            return True
        else:
            self.print_error(f"用户登录失败 (状态码: {response.status_code})")
            if response.text:
                print(f"错误详情: {response.text}")
            return False
    
    def test_create_room(self, room_name: Optional[str] = None) -> Optional[str]:
        """测试创建房间"""
        if not self.token:
            self.print_error("需要先登录才能创建房间")
            return None
            
        if not room_name:
            timestamp = datetime.now().strftime("%H%M%S")
            room_name = f"Python测试房间_{timestamp}"
            
        print(f"\n3. 测试创建房间 (房间名: {room_name})...")
        
        room_data = {
            "name": room_name,
            "description": "通过Python API测试创建的房间",
            "room_type": "public",
            "max_members": 10
        }
        
        response = self.make_request("POST", "/api/rooms/", room_data)
        if not response:
            return None
            
        if response.status_code == 200:
            room = response.json()
            room_id = room.get("id")
            
            self.print_success(f"房间创建成功 (状态码: {response.status_code})")
            self.print_info(f"房间名称: {room.get('name', 'N/A')}")
            self.print_info(f"房间ID: {room_id}")
            self.print_info(f"房间类型: {room.get('room_type', 'N/A')}")
            return room_id
        else:
            self.print_error(f"房间创建失败 (状态码: {response.status_code})")
            if response.text:
                print(f"错误详情: {response.text}")
            return None
    
    def test_join_room(self, room_id: str) -> bool:
        """测试加入房间"""
        if not self.token:
            self.print_error("需要先登录才能加入房间")
            return False
            
        print(f"\n4. 测试加入房间 (房间ID: {room_id})...")
        
        response = self.make_request("POST", f"/api/rooms/{room_id}/join", {})
        if not response:
            return False
            
        if response.status_code == 200:
            result = response.json()
            self.print_success(f"房间加入成功 (状态码: {response.status_code})")
            self.print_info(f"消息: {result.get('message', 'N/A')}")
            return True
        else:
            # 400错误可能是正常的（如已经是成员）
            if response.status_code == 400:
                error_detail = response.json().get("detail", "未知错误")
                if "已经是房间成员" in error_detail:
                    self.print_success(f"房间加入验证正常 - {error_detail}")
                    return True
                else:
                    self.print_error(f"房间加入失败: {error_detail}")
            else:
                self.print_error(f"房间加入失败 (状态码: {response.status_code})")
                if response.text:
                    print(f"错误详情: {response.text}")
            return False
    
    def test_get_current_user(self) -> bool:
        """测试获取当前用户信息"""
        if not self.token:
            self.print_error("需要先登录才能获取用户信息")
            return False
            
        print("\n5. 测试获取当前用户信息...")
        
        response = self.make_request("GET", "/api/auth/me")
        if not response:
            return False
            
        if response.status_code == 200:
            user = response.json()
            self.print_success(f"用户信息获取成功 (状态码: {response.status_code})")
            self.print_info(f"用户ID: {user.get('id', 'N/A')}")
            self.print_info(f"用户名: {user.get('username', 'N/A')}")
            self.print_info(f"角色: {user.get('role', 'N/A')}")
            self.print_info(f"是否可创建房间: {user.get('can_create_room', False)}")
            self.print_info(f"显示名称: {user.get('display_name', 'N/A')}")
            return True
        else:
            self.print_error(f"用户信息获取失败 (状态码: {response.status_code})")
            if response.text:
                print(f"错误详情: {response.text}")
            return False
    
    def run_all_tests(self) -> None:
        """运行所有测试"""
        self.print_section("聊天平台 API 测试")
        
        # 测试1: 获取房间列表
        test1_success = self.test_get_rooms()
        
        # 测试2: 用户登录
        test2_success = self.test_login()
        
        # 测试3: 创建房间
        room_id = None
        if test2_success:
            room_id = self.test_create_room()
            
        # 测试4: 加入房间
        test4_success = False
        if room_id:
            test4_success = self.test_join_room(room_id)
            
        # 测试5: 获取当前用户信息
        test5_success = False
        if test2_success:
            test5_success = self.test_get_current_user()
        
        # 测试总结
        self.print_section("测试总结")
        tests = [
            ("获取房间列表", test1_success),
            ("用户登录", test2_success),
            ("创建房间", room_id is not None),
            ("加入房间", test4_success),
            ("获取用户信息", test5_success)
        ]
        
        passed = sum(1 for _, success in tests if success)
        total = len(tests)
        
        for test_name, success in tests:
            status = "✅ 通过" if success else "❌ 失败"
            print(f"{test_name}: {status}")
            
        print(f"\n总计: {passed}/{total} 个测试通过")
        
        if passed == total:
            self.print_success("所有测试都通过了！🎉")
        else:
            self.print_error(f"有 {total - passed} 个测试失败")

def main():
    """主函数"""
    print("聊天平台 API 测试工具")
    print("使用Python替代PowerShell进行API测试")
    
    # 检查服务器是否运行
    tester = ChatPlatformAPITester()
    
    try:
        response = requests.get(f"{tester.base_url}/api/rooms/", timeout=5)
        print(f"✅ 服务器运行正常 ({tester.base_url})")
    except requests.exceptions.RequestException:
        print(f"❌ 无法连接到服务器 ({tester.base_url})")
        print("请确保服务器正在运行: python -m uvicorn main:app --reload --host 0.0.0.0 --port 8000")
        sys.exit(1)
    
    # 运行所有测试
    tester.run_all_tests()

if __name__ == "__main__":
    main()
