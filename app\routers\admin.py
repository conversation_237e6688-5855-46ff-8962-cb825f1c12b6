"""
管理员后台路由
"""
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_, or_
from pydantic import BaseModel
from datetime import datetime, timezone

from app.core.database import get_db
from app.models.user import User, UserRole, UserStatus
from app.models.room import Room, RoomMember, RoomStatus
from app.models.message import Message
from app.routers.auth import get_current_user


router = APIRouter()


# Pydantic模型
class UserManageRequest(BaseModel):
    user_id: str
    action: str  # 'activate', 'deactivate', 'ban', 'unban', 'grant_room_creation', 'revoke_room_creation'


class RoomManageRequest(BaseModel):
    room_id: str
    action: str  # 'activate', 'deactivate', 'delete'


class AdminStatsResponse(BaseModel):
    total_users: int
    active_users: int
    anonymous_users: int
    total_rooms: int
    active_rooms: int
    total_messages: int
    messages_today: int


class UserListResponse(BaseModel):
    id: str
    username: Optional[str]
    display_name: Optional[str]
    is_anonymous: bool
    role: str
    status: str
    is_active: bool
    can_create_room: bool
    created_at: datetime
    last_login_at: Optional[datetime]


class RoomListResponse(BaseModel):
    id: str
    name: str
    room_type: str
    status: str
    is_active: bool
    member_count: int
    creator_username: Optional[str]
    created_at: datetime


def require_admin(current_user: User = Depends(get_current_user)):
    """要求管理员权限"""
    # 检查是否是匿名用户
    if current_user.is_anonymous:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="需要登录"
        )

    # 检查是否是管理员
    if current_user.role != UserRole.ADMIN:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="需要管理员权限"
        )
    return current_user


@router.get("/stats", response_model=AdminStatsResponse)
async def get_admin_stats(
    admin_user: User = Depends(require_admin),
    db: AsyncSession = Depends(get_db)
):
    """获取管理员统计信息"""
    # 用户统计
    total_users = await db.scalar(select(func.count(User.id)))
    active_users = await db.scalar(
        select(func.count(User.id)).where(
            and_(User.is_active == True, User.is_anonymous == False)
        )
    )
    anonymous_users = await db.scalar(
        select(func.count(User.id)).where(User.is_anonymous == True)
    )

    # 房间统计
    total_rooms = await db.scalar(select(func.count(Room.id)))
    active_rooms = await db.scalar(
        select(func.count(Room.id)).where(Room.is_active == True)
    )

    # 消息统计
    total_messages = await db.scalar(select(func.count(Message.id)))
    today = datetime.now(timezone.utc).date()
    messages_today = await db.scalar(
        select(func.count(Message.id)).where(
            func.date(Message.created_at) == today
        )
    )

    return AdminStatsResponse(
        total_users=total_users or 0,
        active_users=active_users or 0,
        anonymous_users=anonymous_users or 0,
        total_rooms=total_rooms or 0,
        active_rooms=active_rooms or 0,
        total_messages=total_messages or 0,
        messages_today=messages_today or 0
    )


@router.get("/users", response_model=List[UserListResponse])
async def get_users(
    admin_user: User = Depends(require_admin),
    db: AsyncSession = Depends(get_db),
    page: int = Query(1, ge=1),
    size: int = Query(20, ge=1, le=100),
    search: Optional[str] = Query(None),
    role: Optional[str] = Query(None),
    status: Optional[str] = Query(None)
):
    """获取用户列表"""
    query = select(User)

    # 搜索过滤
    if search:
        query = query.where(
            or_(
                User.username.ilike(f"%{search}%"),
                User.display_name.ilike(f"%{search}%")
            )
        )

    # 角色过滤
    if role:
        query = query.where(User.role == UserRole(role))

    # 状态过滤
    if status:
        query = query.where(User.status == UserStatus(status))

    # 分页
    offset = (page - 1) * size
    query = query.offset(offset).limit(size).order_by(User.created_at.desc())

    result = await db.execute(query)
    users = result.scalars().all()

    return [
        UserListResponse(
            id=str(user.id),
            username=user.username,
            display_name=user.display_name,
            is_anonymous=user.is_anonymous,
            role=user.role.value,
            status=user.status.value,
            is_active=user.is_active,
            can_create_room=user.can_create_room,
            created_at=user.created_at,
            last_login_at=user.last_login_at
        )
        for user in users
    ]


@router.post("/users/manage")
async def manage_user(
    request: UserManageRequest,
    admin_user: User = Depends(require_admin),
    db: AsyncSession = Depends(get_db)
):
    """管理用户"""
    # 获取目标用户
    result = await db.execute(select(User).where(User.id == request.user_id))
    user = result.scalar_one_or_none()

    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )

    # 不能管理其他管理员
    if user.role == UserRole.ADMIN and user.id != admin_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="不能管理其他管理员"
        )

    # 执行操作
    if request.action == "activate":
        user.is_active = True
        user.status = UserStatus.ACTIVE
    elif request.action == "deactivate":
        user.is_active = False
        user.status = UserStatus.INACTIVE
    elif request.action == "ban":
        user.is_active = False
        user.status = UserStatus.BANNED
    elif request.action == "unban":
        user.is_active = True
        user.status = UserStatus.ACTIVE
    elif request.action == "grant_room_creation":
        user.can_create_room = True
    elif request.action == "revoke_room_creation":
        user.can_create_room = False
    else:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="无效的操作"
        )

    await db.commit()

    return {"message": f"用户 {user.username or user.id} 操作成功"}


@router.get("/rooms", response_model=List[RoomListResponse])
async def get_rooms(
    admin_user: User = Depends(require_admin),
    db: AsyncSession = Depends(get_db),
    page: int = Query(1, ge=1),
    size: int = Query(20, ge=1, le=100),
    search: Optional[str] = Query(None)
):
    """获取房间列表"""
    query = select(Room).join(User, Room.creator_id == User.id)

    # 搜索过滤
    if search:
        query = query.where(Room.name.ilike(f"%{search}%"))

    # 分页
    offset = (page - 1) * size
    query = query.offset(offset).limit(size).order_by(Room.created_at.desc())

    result = await db.execute(query)
    rooms = result.scalars().all()

    # 获取房间成员数量
    room_responses = []
    for room in rooms:
        member_count = await db.scalar(
            select(func.count()).select_from(
                select(User.id).join(RoomMember, User.id == RoomMember.user_id)
                .where(RoomMember.room_id == room.id)
            )
        )

        # 获取创建者用户名
        creator_result = await db.execute(
            select(User.username).where(User.id == room.creator_id)
        )
        creator_username = creator_result.scalar_one_or_none()

        room_responses.append(RoomListResponse(
            id=str(room.id),
            name=room.name,
            room_type=room.room_type.value,
            status=room.status.value,
            is_active=room.is_active,
            member_count=member_count or 0,
            creator_username=creator_username,
            created_at=room.created_at
        ))

    return room_responses


@router.post("/rooms/manage")
async def manage_room(
    request: RoomManageRequest,
    admin_user: User = Depends(require_admin),
    db: AsyncSession = Depends(get_db)
):
    """管理房间"""
    # 获取目标房间
    result = await db.execute(select(Room).where(Room.id == request.room_id))
    room = result.scalar_one_or_none()

    if not room:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="房间不存在"
        )

    # 执行操作
    if request.action == "activate":
        room.is_active = True
        room.status = RoomStatus.ACTIVE
    elif request.action == "deactivate":
        room.is_active = False
        room.status = RoomStatus.INACTIVE
    elif request.action == "delete":
        # 软删除，设置为非活跃状态
        room.is_active = False
        room.status = RoomStatus.INACTIVE
    else:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="无效的操作"
        )

    await db.commit()

    return {"message": f"房间 {room.name} 操作成功"}