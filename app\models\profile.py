"""
人物信息模型
"""
import uuid
from datetime import datetime, timezone
from sqlalchemy import Column, String, Boolean, DateTime, Text, Enum, ForeignKey, Integer, JSON
from sqlalchemy.orm import relationship
import enum

from app.core.database import Base


class ProfileFieldType(enum.Enum):
    """人物信息字段类型枚举"""
    SINGLE_CHOICE = "single_choice"    # 单选
    MULTIPLE_CHOICE = "multiple_choice"  # 多选
    TEXT = "text"                      # 文本
    NUMBER = "number"                  # 数字


class ProfileCategory(Base):
    """人物信息类别模型（管理员可配置）"""
    __tablename__ = "profile_categories"

    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    name = Column(String(50), nullable=False, unique=True)  # 类别名称，如"职业身份"
    description = Column(Text, nullable=True)

    # 字段类型和配置
    field_type = Column(Enum(ProfileFieldType), default=ProfileFieldType.SINGLE_CHOICE)
    options = Column(JSON, nullable=True)  # 选项列表，如["学生", "教师", "工程师"]
    max_selections = Column(Integer, nullable=True)  # 多选时的最大选择数
    is_required = Column(Boolean, default=False)  # 是否必填

    # 显示设置
    display_order = Column(Integer, default=0)  # 显示顺序
    is_active = Column(Boolean, default=True)

    # 时间戳
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    updated_at = Column(DateTime, default=lambda: datetime.now(timezone.utc),
                       onupdate=lambda: datetime.now(timezone.utc))

    # 关系
    user_profiles = relationship("UserProfile", back_populates="category")

    def __repr__(self):
        return f"<ProfileCategory {self.name}>"


class UserProfile(Base):
    """用户人物信息模型"""
    __tablename__ = "user_profiles"

    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    user_id = Column(String(36), ForeignKey("users.id"), nullable=False)
    category_id = Column(String(36), ForeignKey("profile_categories.id"), nullable=False)

    # 信息值
    value = Column(JSON, nullable=True)  # 存储用户选择的值，可以是字符串、数组等

    # 隐私设置
    is_hidden = Column(Boolean, default=False)  # 是否隐藏
    revealed_at = Column(DateTime, nullable=True)  # 揭露时间
    revealed_in_room_id = Column(String(36), ForeignKey("rooms.id"), nullable=True)

    # 时间戳
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    updated_at = Column(DateTime, default=lambda: datetime.now(timezone.utc),
                       onupdate=lambda: datetime.now(timezone.utc))

    # 关系
    user = relationship("User", back_populates="user_profiles")
    category = relationship("ProfileCategory", back_populates="user_profiles")
    revealed_in_room = relationship("Room")

    def __repr__(self):
        return f"<UserProfile {self.user_id} - {self.category_id}>"

    def reveal_in_room(self, room_id):
        """在指定房间中揭露信息"""
        if not self.is_hidden:
            return False, "信息已经是公开的"

        self.is_hidden = False
        self.revealed_at = datetime.now(timezone.utc)
        self.revealed_in_room_id = room_id
        return True, "信息已揭露"

    def hide(self):
        """隐藏信息"""
        self.is_hidden = True
        self.revealed_at = None
        self.revealed_in_room_id = None
        return True, "信息已隐藏"

    @property
    def display_value(self):
        """获取显示值"""
        if self.is_hidden:
            return "***隐藏***"

        if isinstance(self.value, list):
            return ", ".join(str(v) for v in self.value)
        return str(self.value) if self.value else ""

    def to_dict(self, show_hidden=False):
        """转换为字典格式"""
        return {
            "id": str(self.id),
            "category_name": self.category.name,
            "category_type": self.category.field_type.value,
            "value": self.value if (show_hidden or not self.is_hidden) else None,
            "display_value": self.display_value if (show_hidden or not self.is_hidden) else "***隐藏***",
            "is_hidden": self.is_hidden,
            "revealed_at": self.revealed_at.isoformat() if self.revealed_at else None,
            "created_at": self.created_at.isoformat(),
        }