<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>聊天平台</title>
    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>💬</text></svg>">
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        /* 确保页面不会超出屏幕 */
        html, body {
            height: 100%;
            overflow: hidden;
        }

        .main-container {
            height: calc(100vh - 4rem);
            max-height: calc(100vh - 4rem);
        }

        .chat-container {
            height: 100%;
            max-height: 100%;
        }

        .message-bubble {
            max-width: 70%;
            word-wrap: break-word;
        }
        .message-bubble.own {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .message-bubble.other {
            background: #f1f5f9;
            color: #334155;
        }
        .typing-indicator {
            display: none;
        }
        .typing-indicator.show {
            display: flex;
        }
        .dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background-color: #94a3b8;
            animation: typing 1.4s infinite ease-in-out;
        }
        .dot:nth-child(1) { animation-delay: -0.32s; }
        .dot:nth-child(2) { animation-delay: -0.16s; }
        @keyframes typing {
            0%, 80%, 100% { transform: scale(0); }
            40% { transform: scale(1); }
        }

        /* 禁用状态样式 */
        .btn-disabled {
            opacity: 0.5;
            cursor: not-allowed;
            pointer-events: none;
        }
    </style>
</head>
<body class="bg-gray-100">
    <!-- 导航栏 -->
    <nav class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <i class="fas fa-comments text-2xl text-blue-600 mr-3"></i>
                    <h1 class="text-xl font-semibold text-gray-900">聊天平台</h1>
                </div>
                <div class="flex items-center space-x-4">
                    <div id="user-info" class="hidden">
                        <span id="user-name" class="text-sm text-gray-700"></span>
                        <button id="logout-btn" class="ml-2 text-sm text-red-600 hover:text-red-800">登出</button>
                    </div>
                    <div id="auth-buttons" class="space-x-2">
                        <button id="login-btn" class="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700">登录</button>
                        <button id="register-btn" class="px-4 py-2 text-sm font-medium text-blue-600 bg-white border border-blue-600 rounded-md hover:bg-blue-50">注册</button>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主要内容区域 -->
    <div class="flex main-container bg-gray-100">
        <!-- 侧边栏 -->
        <div class="w-64 bg-white shadow-sm border-r">
            <div class="p-4">
                <h2 class="text-lg font-semibold text-gray-900 mb-4">房间列表</h2>
                <div id="room-list" class="space-y-2">
                    <!-- 房间列表将在这里动态加载 -->
                </div>
                <button id="create-room-btn" class="w-full mt-4 px-4 py-2 text-sm font-medium text-white bg-green-600 rounded-md hover:bg-green-700">
                    <i class="fas fa-plus mr-2"></i>创建房间
                </button>
            </div>
        </div>

        <!-- 聊天区域 -->
        <div class="flex-1 flex flex-col chat-container">
            <div id="chat-header" class="bg-white border-b px-6 py-4 hidden">
                <h3 id="room-name" class="text-lg font-semibold text-gray-900"></h3>
                <p id="room-members" class="text-sm text-gray-500"></p>
            </div>

            <div id="welcome-screen" class="flex-1 flex items-center justify-center">
                <div class="text-center">
                    <i class="fas fa-comments text-6xl text-gray-300 mb-4"></i>
                    <h2 class="text-2xl font-semibold text-gray-600 mb-2">欢迎来到聊天平台</h2>
                    <p class="text-gray-500">选择一个房间开始聊天，或创建一个新房间</p>
                </div>
            </div>

            <div id="chat-area" class="flex-1 flex flex-col hidden">
                <div id="messages-container" class="flex-1 overflow-y-auto p-4 space-y-4">
                    <!-- 消息将在这里显示 -->
                </div>

                <!-- 输入区域 -->
                <div class="bg-white border-t p-4">
                    <div class="flex items-center space-x-2">
                        <button id="emoji-btn" class="p-2 text-gray-500 hover:text-gray-700">
                            <i class="fas fa-smile"></i>
                        </button>
                        <button id="file-btn" class="p-2 text-gray-500 hover:text-gray-700">
                            <i class="fas fa-paperclip"></i>
                        </button>
                        <input type="file" id="file-input" class="hidden" accept="image/*,audio/*">
                        <div class="flex-1 relative">
                            <input
                                type="text"
                                id="message-input"
                                placeholder="输入消息..."
                                class="w-full px-4 py-2 border border-gray-300 rounded-full focus:outline-none focus:ring-2 focus:ring-blue-500"
                            >
                        </div>
                        <button id="send-btn" class="px-6 py-2 bg-blue-600 text-white rounded-full hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <i class="fas fa-paper-plane"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 登录模态框 -->
    <div id="login-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden items-center justify-center">
        <div class="bg-white rounded-lg p-6 w-96">
            <h3 class="text-lg font-semibold mb-4">用户登录</h3>
            <form id="login-form">
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">用户名或邮箱</label>
                    <input type="text" id="login-username" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                </div>
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-2">密码</label>
                    <input type="password" id="login-password" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                </div>
                <div class="flex justify-end space-x-2">
                    <button type="button" id="login-cancel" class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300">取消</button>
                    <button type="submit" class="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700">登录</button>
                </div>
            </form>
        </div>
    </div>

    <!-- 注册模态框 -->
    <div id="register-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden items-center justify-center">
        <div class="bg-white rounded-lg p-6 w-96">
            <h3 class="text-lg font-semibold mb-4">用户注册</h3>
            <form id="register-form">
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">用户名</label>
                    <input type="text" id="register-username" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                </div>
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">邮箱</label>
                    <input type="email" id="register-email" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                </div>
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-2">密码</label>
                    <input type="password" id="register-password" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                </div>
                <div class="flex justify-end space-x-2">
                    <button type="button" id="register-cancel" class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300">取消</button>
                    <button type="submit" class="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700">注册</button>
                </div>
            </form>
        </div>
    </div>

    <script src="/static/js/app.js"></script>
</body>
</html>