"""
认证路由
"""
from datetime import timed<PERSON><PERSON>
from typing import Optional
from fastapi import APIRouter, Depends, HTTPException, status, Response, Request, Cookie
from fastapi.security import HTT<PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.ext.asyncio import AsyncSession
from pydantic import BaseModel, EmailStr

from app.core.database import get_db
from app.core.config import settings
from app.services.auth_service import (
    authenticate_user, register_user, create_anonymous_user,
    get_user_by_anonymous_id, create_access_token, verify_token, get_user_by_id
)
from app.models.user import User


router = APIRouter()
security = HTTPBearer(auto_error=False)


# Pydantic模型
class LoginRequest(BaseModel):
    username: str
    password: str


class RegisterRequest(BaseModel):
    username: str
    email: EmailStr
    password: str


class TokenResponse(BaseModel):
    access_token: str
    token_type: str
    user: dict


class UserResponse(BaseModel):
    id: str
    username: Optional[str]
    email: Optional[str]
    display_name: Optional[str]
    is_anonymous: bool
    role: str
    can_create_room: bool


async def get_current_user(
    request: Request,
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security),
    anonymous_id: Optional[str] = Cookie(None),
    db: AsyncSession = Depends(get_db)
) -> User:
    """获取当前用户（支持JWT令牌和匿名用户）"""

    # 尝试从JWT令牌获取用户
    if credentials:
        payload = verify_token(credentials.credentials)
        if payload:
            user_id = payload.get("sub")
            if user_id:
                user = await get_user_by_id(db, user_id)
                if user and user.is_active:
                    return user

    # 尝试从cookie获取匿名用户
    if anonymous_id:
        user = await get_user_by_anonymous_id(db, anonymous_id)
        if user:
            return user

    # 创建新的匿名用户
    import uuid
    new_anonymous_id = str(uuid.uuid4())
    user = await create_anonymous_user(db, new_anonymous_id)
    return user


def user_to_dict(user: User) -> dict:
    """将用户对象转换为字典"""
    return {
        "id": str(user.id),
        "username": user.username,
        "email": user.email,
        "display_name": user.display_name,
        "display_identifier": user.display_identifier,
        "is_anonymous": user.is_anonymous,
        "role": user.role.value,
        "can_create_room": user.can_create_room,
        "created_at": user.created_at.isoformat(),
    }


@router.post("/login", response_model=TokenResponse)
async def login(
    login_data: LoginRequest,
    response: Response,
    db: AsyncSession = Depends(get_db)
):
    """用户登录"""
    user = await authenticate_user(db, login_data.username, login_data.password)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用户名或密码错误",
            headers={"WWW-Authenticate": "Bearer"},
        )

    # 创建访问令牌
    access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": str(user.id)}, expires_delta=access_token_expires
    )

    # 设置匿名用户cookie（如果用户有匿名ID）
    if user.anonymous_id:
        response.set_cookie(
            key="anonymous_id",
            value=user.anonymous_id,
            max_age=30 * 24 * 60 * 60,  # 30天
            httponly=True,
            secure=not settings.DEBUG
        )

    return {
        "access_token": access_token,
        "token_type": "bearer",
        "user": user_to_dict(user)
    }


@router.post("/register", response_model=TokenResponse)
async def register(
    register_data: RegisterRequest,
    response: Response,
    anonymous_id: Optional[str] = Cookie(None),
    db: AsyncSession = Depends(get_db)
):
    """用户注册"""
    result = await register_user(
        db, register_data.username, register_data.email,
        register_data.password, anonymous_id
    )

    if isinstance(result, str):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=result
        )

    user = result

    # 创建访问令牌
    access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": str(user.id)}, expires_delta=access_token_expires
    )

    # 设置匿名用户cookie
    response.set_cookie(
        key="anonymous_id",
        value=user.anonymous_id,
        max_age=30 * 24 * 60 * 60,  # 30天
        httponly=True,
        secure=not settings.DEBUG
    )

    return {
        "access_token": access_token,
        "token_type": "bearer",
        "user": user_to_dict(user)
    }


@router.get("/me", response_model=UserResponse)
async def get_current_user_info(
    response: Response,
    current_user: User = Depends(get_current_user)
):
    """获取当前用户信息"""
    # 如果是匿名用户，设置cookie
    if current_user.is_anonymous:
        response.set_cookie(
            key="anonymous_id",
            value=current_user.anonymous_id,
            max_age=30 * 24 * 60 * 60,  # 30天
            httponly=True,
            secure=not settings.DEBUG
        )

    return UserResponse(
        id=str(current_user.id),
        username=current_user.username,
        email=current_user.email,
        display_name=current_user.display_name,
        is_anonymous=current_user.is_anonymous,
        role=current_user.role.value,
        can_create_room=current_user.can_create_room
    )


@router.post("/logout")
async def logout(response: Response):
    """用户登出"""
    response.delete_cookie("anonymous_id")
    return {"message": "已成功登出"}


@router.post("/switch-anonymous")
async def switch_to_anonymous(
    response: Response,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """切换到匿名模式"""
    if current_user.is_anonymous:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="当前已是匿名用户"
        )

    # 创建新的匿名用户
    import uuid
    new_anonymous_id = str(uuid.uuid4())
    anonymous_user = await create_anonymous_user(db, new_anonymous_id)

    # 设置新的匿名cookie
    response.set_cookie(
        key="anonymous_id",
        value=anonymous_user.anonymous_id,
        max_age=30 * 24 * 60 * 60,  # 30天
        httponly=True,
        secure=not settings.DEBUG
    )

    return {
        "message": "已切换到匿名模式",
        "user": user_to_dict(anonymous_user)
    }