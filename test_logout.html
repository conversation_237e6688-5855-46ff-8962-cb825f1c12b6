<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登出功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            border: 1px solid #ddd;
            padding: 20px;
            margin: 20px 0;
            border-radius: 5px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .logout-btn {
            background: #dc3545;
        }
        .logout-btn:hover {
            background: #c82333;
        }
        .info {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <h1>登出功能测试页面</h1>
    
    <div class="test-section">
        <h2>当前状态</h2>
        <div id="status" class="info">
            <p>Token: <span id="token-status">未检查</span></p>
            <p>用户: <span id="user-status">未检查</span></p>
            <p>Cookie: <span id="cookie-status">未检查</span></p>
        </div>
        <button onclick="checkStatus()">检查状态</button>
    </div>

    <div class="test-section">
        <h2>登录测试</h2>
        <input type="text" id="username" placeholder="用户名" value="admin">
        <input type="password" id="password" placeholder="密码" value="admin123">
        <button onclick="login()">登录</button>
    </div>

    <div class="test-section">
        <h2>登出测试</h2>
        <button class="logout-btn" onclick="logout()">登出</button>
        <button onclick="logoutAndCheck()">登出并检查状态</button>
    </div>

    <div class="test-section">
        <h2>测试日志</h2>
        <div id="log"></div>
        <button onclick="clearLog()">清空日志</button>
    </div>

    <script>
        let token = localStorage.getItem('access_token');
        let currentUser = null;

        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = type;
            logEntry.innerHTML = `<strong>[${timestamp}]</strong> ${message}`;
            logDiv.appendChild(logEntry);
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }

        function getCookie(name) {
            const value = `; ${document.cookie}`;
            const parts = value.split(`; ${name}=`);
            if (parts.length === 2) return parts.pop().split(';').shift();
            return null;
        }

        function checkStatus() {
            token = localStorage.getItem('access_token');
            const anonymousCookie = getCookie('anonymous_id');
            
            document.getElementById('token-status').textContent = token ? '存在' : '不存在';
            document.getElementById('cookie-status').textContent = anonymousCookie ? `存在: ${anonymousCookie}` : '不存在';
            
            if (token) {
                // 尝试获取用户信息
                fetch('/api/rooms/', {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                })
                .then(response => {
                    if (response.ok) {
                        document.getElementById('user-status').textContent = '已认证';
                        log('用户状态检查: 已认证', 'success');
                    } else {
                        document.getElementById('user-status').textContent = '未认证';
                        log('用户状态检查: 未认证', 'error');
                    }
                })
                .catch(error => {
                    document.getElementById('user-status').textContent = '检查失败';
                    log(`用户状态检查失败: ${error.message}`, 'error');
                });
            } else {
                document.getElementById('user-status').textContent = '无Token';
                log('用户状态: 无Token', 'info');
            }
        }

        async function login() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            try {
                log(`尝试登录: ${username}`, 'info');
                const response = await fetch('/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ username, password })
                });

                if (response.ok) {
                    const data = await response.json();
                    token = data.access_token;
                    localStorage.setItem('access_token', token);
                    currentUser = data.user;
                    log('登录成功', 'success');
                    checkStatus();
                } else {
                    const error = await response.json();
                    log(`登录失败: ${error.detail}`, 'error');
                }
            } catch (error) {
                log(`登录错误: ${error.message}`, 'error');
            }
        }

        async function logout() {
            try {
                log('开始登出...', 'info');
                
                // 调用后端登出API
                const response = await fetch('/api/auth/logout', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });

                // 清除前端状态
                token = null;
                currentUser = null;
                localStorage.removeItem('access_token');

                // 清除所有可能的cookie
                clearAllCookies();

                if (response.ok) {
                    const data = await response.json();
                    log(`后端登出成功: ${data.message}`, 'success');
                } else {
                    log('后端登出失败，但前端状态已清理', 'error');
                }

                log('前端状态已清理', 'success');
                
            } catch (error) {
                log(`登出错误: ${error.message}`, 'error');
                // 即使出错也要清理前端状态
                token = null;
                currentUser = null;
                localStorage.removeItem('access_token');
                clearAllCookies();
                log('前端状态已强制清理', 'info');
            }
        }

        function clearAllCookies() {
            // 获取所有可能的cookie路径和域名组合
            const paths = ['/', '/api', '/api/auth'];
            const domains = [window.location.hostname, '.' + window.location.hostname, 'localhost', '.localhost'];
            
            // 清除anonymous_id cookie的所有可能组合
            paths.forEach(path => {
                // 不指定域名
                document.cookie = `anonymous_id=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=${path};`;
                document.cookie = `anonymous_id=; max-age=0; path=${path};`;
                
                // 指定各种域名
                domains.forEach(domain => {
                    try {
                        document.cookie = `anonymous_id=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=${path}; domain=${domain};`;
                        document.cookie = `anonymous_id=; max-age=0; path=${path}; domain=${domain};`;
                    } catch (e) {
                        // 忽略域名设置错误
                    }
                });
            });
            log('Cookie清理完成', 'info');
        }

        async function logoutAndCheck() {
            await logout();
            setTimeout(() => {
                checkStatus();
            }, 500);
        }

        // 页面加载时检查状态
        window.onload = function() {
            checkStatus();
        };
    </script>
</body>
</html>
