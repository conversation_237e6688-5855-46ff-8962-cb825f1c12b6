<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>房间功能修复测试</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gray-100">
    <div class="container mx-auto p-6">
        <h1 class="text-3xl font-bold text-center mb-8">房间功能修复测试</h1>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- 测试1: 创建房间UI -->
            <div class="bg-white rounded-lg shadow p-6">
                <h2 class="text-xl font-semibold mb-4">测试1: 创建房间UI美化</h2>
                <p class="text-gray-600 mb-4">测试新的创建房间模态框UI</p>
                <button id="test-create-room" class="w-full px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700">
                    <i class="fas fa-plus mr-2"></i>测试创建房间模态框
                </button>
                <div id="create-room-result" class="mt-4 p-3 rounded hidden"></div>
            </div>

            <!-- 测试2: 房间加入功能 -->
            <div class="bg-white rounded-lg shadow p-6">
                <h2 class="text-xl font-semibold mb-4">测试2: 房间加入功能修复</h2>
                <p class="text-gray-600 mb-4">测试房间加入API的422错误修复</p>
                <div class="space-y-3">
                    <button id="test-login" class="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700">
                        <i class="fas fa-sign-in-alt mr-2"></i>先登录测试账户
                    </button>
                    <button id="test-create-test-room" class="w-full px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700">
                        <i class="fas fa-home mr-2"></i>创建测试房间
                    </button>
                    <button id="test-join-room" class="w-full px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700">
                        <i class="fas fa-door-open mr-2"></i>测试加入房间
                    </button>
                </div>
                <div id="join-room-result" class="mt-4 p-3 rounded hidden"></div>
            </div>
        </div>

        <!-- 日志区域 -->
        <div class="mt-8 bg-white rounded-lg shadow p-6">
            <h2 class="text-xl font-semibold mb-4">测试日志</h2>
            <div id="test-log" class="bg-gray-50 p-4 rounded-lg h-64 overflow-y-auto font-mono text-sm"></div>
        </div>
    </div>

    <!-- 创建房间模态框 (复制自主页面) -->
    <div id="create-room-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden items-center justify-center z-50">
        <div class="bg-white rounded-lg shadow-xl p-6 w-96 max-w-md mx-4">
            <div class="flex items-center justify-between mb-6">
                <h3 class="text-xl font-semibold text-gray-900">创建新房间</h3>
                <button id="create-room-close" class="text-gray-400 hover:text-gray-600 transition-colors">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
            <form id="create-room-form">
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-home mr-2 text-green-600"></i>房间名称
                    </label>
                    <input type="text" id="room-name" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all" placeholder="请输入房间名称" required>
                </div>
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-align-left mr-2 text-green-600"></i>房间描述
                    </label>
                    <textarea id="room-description" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all resize-none" rows="3" placeholder="请输入房间描述（可选）"></textarea>
                </div>
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-shield-alt mr-2 text-green-600"></i>房间类型
                    </label>
                    <select id="room-type" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all">
                        <option value="public">公开房间</option>
                        <option value="private" selected>私有房间</option>
                        <option value="password">密码房间</option>
                    </select>
                </div>
                <div id="password-field" class="mb-4 hidden">
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-key mr-2 text-green-600"></i>房间密码
                    </label>
                    <input type="password" id="room-password" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all" placeholder="请输入房间密码">
                </div>
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-users mr-2 text-green-600"></i>最大成员数
                    </label>
                    <input type="number" id="room-max-members" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all" value="100" min="2" max="1000">
                </div>
                <div class="flex justify-end space-x-3">
                    <button type="button" id="create-room-cancel" class="px-6 py-3 text-sm font-medium text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors">取消</button>
                    <button type="submit" class="px-6 py-3 text-sm font-medium text-white bg-green-600 rounded-lg hover:bg-green-700 transition-colors">
                        <i class="fas fa-plus mr-2"></i>创建房间
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script>
        let currentUser = null;
        let testRoomId = null;
        
        function log(message) {
            const logElement = document.getElementById('test-log');
            const timestamp = new Date().toLocaleTimeString();
            logElement.innerHTML += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }

        function getAuthHeaders() {
            const token = localStorage.getItem('access_token');
            return token ? { 'Authorization': `Bearer ${token}` } : {};
        }

        // 测试1: 创建房间UI
        document.getElementById('test-create-room').addEventListener('click', () => {
            log('测试创建房间模态框UI...');
            showCreateRoomModal();
        });

        function showCreateRoomModal() {
            const modal = document.getElementById('create-room-modal');
            modal.classList.remove('hidden');
            modal.classList.add('flex');
            log('✅ 创建房间模态框已显示');
        }

        function hideCreateRoomModal() {
            const modal = document.getElementById('create-room-modal');
            modal.classList.add('hidden');
            modal.classList.remove('flex');
            log('✅ 创建房间模态框已隐藏');
        }

        // 模态框事件
        document.getElementById('create-room-close').addEventListener('click', hideCreateRoomModal);
        document.getElementById('create-room-cancel').addEventListener('click', hideCreateRoomModal);

        // 房间类型选择
        document.getElementById('room-type').addEventListener('change', (e) => {
            const passwordField = document.getElementById('password-field');
            if (e.target.value === 'password') {
                passwordField.classList.remove('hidden');
                document.getElementById('room-password').required = true;
                log('✅ 密码字段已显示');
            } else {
                passwordField.classList.add('hidden');
                document.getElementById('room-password').required = false;
                log('✅ 密码字段已隐藏');
            }
        });

        // 测试2: 登录
        document.getElementById('test-login').addEventListener('click', async () => {
            log('尝试登录测试账户...');
            try {
                const response = await fetch('/api/auth/login', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ username: 'admin', password: 'admin123' })
                });

                if (response.ok) {
                    const data = await response.json();
                    localStorage.setItem('access_token', data.access_token);
                    currentUser = data.user;
                    log('✅ 登录成功: ' + currentUser.username);
                } else {
                    const error = await response.json();
                    log('❌ 登录失败: ' + error.detail);
                }
            } catch (error) {
                log('❌ 登录错误: ' + error.message);
            }
        });

        // 测试3: 创建测试房间
        document.getElementById('test-create-test-room').addEventListener('click', async () => {
            if (!currentUser) {
                log('❌ 请先登录');
                return;
            }

            log('创建测试房间...');
            try {
                const response = await fetch('/api/rooms/', {
                    method: 'POST',
                    headers: {
                        ...getAuthHeaders(),
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        name: '测试房间_' + Date.now(),
                        description: '用于测试房间加入功能的房间',
                        room_type: 'public',
                        max_members: 10
                    })
                });

                if (response.ok) {
                    const room = await response.json();
                    testRoomId = room.id;
                    log('✅ 测试房间创建成功: ' + room.name + ' (ID: ' + room.id + ')');
                } else {
                    const error = await response.json();
                    log('❌ 创建房间失败: ' + error.detail);
                }
            } catch (error) {
                log('❌ 创建房间错误: ' + error.message);
            }
        });

        // 测试4: 加入房间
        document.getElementById('test-join-room').addEventListener('click', async () => {
            if (!testRoomId) {
                log('❌ 请先创建测试房间');
                return;
            }

            log('测试加入房间 (修复422错误)...');
            try {
                const response = await fetch(`/api/rooms/${testRoomId}/join`, {
                    method: 'POST',
                    headers: {
                        ...getAuthHeaders(),
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({}) // 发送空的请求体
                });

                if (response.ok) {
                    const result = await response.json();
                    log('✅ 房间加入成功: ' + result.message);
                    document.getElementById('join-room-result').className = 'mt-4 p-3 rounded bg-green-100 text-green-800';
                    document.getElementById('join-room-result').textContent = '✅ 房间加入功能修复成功！';
                    document.getElementById('join-room-result').classList.remove('hidden');
                } else {
                    const error = await response.json();
                    log('❌ 加入房间失败: ' + error.detail + ' (状态码: ' + response.status + ')');
                    document.getElementById('join-room-result').className = 'mt-4 p-3 rounded bg-red-100 text-red-800';
                    document.getElementById('join-room-result').textContent = '❌ 房间加入失败: ' + error.detail;
                    document.getElementById('join-room-result').classList.remove('hidden');
                }
            } catch (error) {
                log('❌ 加入房间错误: ' + error.message);
                document.getElementById('join-room-result').className = 'mt-4 p-3 rounded bg-red-100 text-red-800';
                document.getElementById('join-room-result').textContent = '❌ 网络错误: ' + error.message;
                document.getElementById('join-room-result').classList.remove('hidden');
            }
        });

        // 初始化日志
        log('房间功能修复测试页面已加载');
        log('测试项目:');
        log('1. 创建房间UI美化 - 新的模态框界面');
        log('2. 房间加入422错误修复 - 发送正确的请求体');
    </script>
</body>
</html>
