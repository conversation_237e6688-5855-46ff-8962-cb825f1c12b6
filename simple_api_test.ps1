# 简单的API测试脚本
Write-Host "=== 聊天平台 API 测试 ===" -ForegroundColor Green

# 1. 测试获取房间列表
Write-Host "`n1. 测试获取房间列表..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "http://localhost:8000/api/rooms/" -Method GET
    Write-Host "✅ 房间列表获取成功 (状态码: $($response.StatusCode))" -ForegroundColor Green
    $rooms = $response.Content | ConvertFrom-Json
    Write-Host "房间数量: $($rooms.Count)" -ForegroundColor Cyan
} catch {
    Write-Host "❌ 房间列表获取失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 2. 测试用户登录
Write-Host "`n2. 测试用户登录..." -ForegroundColor Yellow
try {
    $loginData = '{"username":"admin","password":"admin123"}'
    $response = Invoke-WebRequest -Uri "http://localhost:8000/api/auth/login" -Method POST -Headers @{"Content-Type"="application/json"} -Body $loginData
    Write-Host "✅ 用户登录成功 (状态码: $($response.StatusCode))" -ForegroundColor Green
    
    $loginResult = $response.Content | ConvertFrom-Json
    $token = $loginResult.access_token
    Write-Host "用户名: $($loginResult.user.username)" -ForegroundColor Cyan
    Write-Host "角色: $($loginResult.user.role)" -ForegroundColor Cyan
    
    # 3. 测试创建房间
    Write-Host "`n3. 测试创建房间..." -ForegroundColor Yellow
    try {
        $roomData = '{"name":"PowerShell测试房间","description":"API测试房间","room_type":"public","max_members":10}'
        $headers = @{
            "Content-Type" = "application/json"
            "Authorization" = "Bearer $token"
        }
        $response = Invoke-WebRequest -Uri "http://localhost:8000/api/rooms/" -Method POST -Headers $headers -Body $roomData
        Write-Host "✅ 房间创建成功 (状态码: $($response.StatusCode))" -ForegroundColor Green
        
        $newRoom = $response.Content | ConvertFrom-Json
        $roomId = $newRoom.id
        Write-Host "房间名称: $($newRoom.name)" -ForegroundColor Cyan
        Write-Host "房间ID: $roomId" -ForegroundColor Cyan
        
        # 4. 测试加入房间
        Write-Host "`n4. 测试加入房间..." -ForegroundColor Yellow
        try {
            $response = Invoke-WebRequest -Uri "http://localhost:8000/api/rooms/$roomId/join" -Method POST -Headers $headers -Body '{}'
            Write-Host "✅ 房间加入成功 (状态码: $($response.StatusCode))" -ForegroundColor Green
            
            $joinResult = $response.Content | ConvertFrom-Json
            Write-Host "消息: $($joinResult.message)" -ForegroundColor Cyan
        } catch {
            Write-Host "❌ 房间加入失败: $($_.Exception.Message)" -ForegroundColor Red
            if ($_.ErrorDetails.Message) {
                Write-Host "错误详情: $($_.ErrorDetails.Message)" -ForegroundColor Red
            }
        }
        
    } catch {
        Write-Host "❌ 房间创建失败: $($_.Exception.Message)" -ForegroundColor Red
    }
    
} catch {
    Write-Host "❌ 用户登录失败: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n=== 测试完成 ===" -ForegroundColor Green

# 显示正确的PowerShell命令格式
Write-Host "`n=== PowerShell API 命令格式 ===" -ForegroundColor Yellow
Write-Host "获取房间列表:" -ForegroundColor Cyan
Write-Host 'Invoke-WebRequest -Uri "http://localhost:8000/api/rooms/" -Method GET'

Write-Host "`n用户登录:" -ForegroundColor Cyan
Write-Host 'Invoke-WebRequest -Uri "http://localhost:8000/api/auth/login" -Method POST -Headers @{"Content-Type"="application/json"} -Body ''{"username":"admin","password":"admin123"}'''

Write-Host "`n创建房间 (需要token):" -ForegroundColor Cyan
Write-Host '$headers = @{"Content-Type"="application/json";"Authorization"="Bearer YOUR_TOKEN"}'
Write-Host 'Invoke-WebRequest -Uri "http://localhost:8000/api/rooms/" -Method POST -Headers $headers -Body ''{"name":"测试房间","room_type":"public","max_members":10}'''

Write-Host "`n加入房间 (需要token和房间ID):" -ForegroundColor Cyan
Write-Host 'Invoke-WebRequest -Uri "http://localhost:8000/api/rooms/ROOM_ID/join" -Method POST -Headers $headers -Body ''{}'''
