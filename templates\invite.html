<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>加入房间 - 聊天平台</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gray-100 min-h-screen flex items-center justify-center">
    <div class="bg-white rounded-lg shadow-lg p-8 w-96 max-w-md mx-4">
        <div class="text-center mb-6">
            <i class="fas fa-users text-4xl text-blue-500 mb-4"></i>
            <h1 class="text-2xl font-bold text-gray-900 mb-2">加入房间</h1>
            <p class="text-gray-600">您被邀请加入聊天房间</p>
        </div>

        <div id="room-info" class="mb-6 p-4 bg-gray-50 rounded-lg hidden">
            <h3 id="room-name" class="font-semibold text-gray-900 mb-2"></h3>
            <p id="room-description" class="text-sm text-gray-600 mb-2"></p>
            <div class="flex items-center text-sm text-gray-500">
                <i class="fas fa-shield-alt mr-2"></i>
                <span id="room-type"></span>
            </div>
        </div>

        <div id="login-section" class="mb-6 hidden">
            <p class="text-sm text-gray-600 mb-4">
                <i class="fas fa-info-circle mr-1"></i>
                您需要登录才能加入房间
            </p>
            <div class="space-y-3">
                <button id="login-btn" class="w-full bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded-lg">
                    <i class="fas fa-sign-in-alt mr-2"></i>登录
                </button>
                <button id="register-btn" class="w-full bg-green-500 hover:bg-green-600 text-white py-2 px-4 rounded-lg">
                    <i class="fas fa-user-plus mr-2"></i>注册
                </button>
                <button id="anonymous-btn" class="w-full bg-gray-500 hover:bg-gray-600 text-white py-2 px-4 rounded-lg">
                    <i class="fas fa-user-secret mr-2"></i>匿名加入
                </button>
            </div>
        </div>

        <div id="password-section" class="mb-6 hidden">
            <label class="block text-sm font-medium text-gray-700 mb-2">房间密码</label>
            <input type="password" id="room-password" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入房间密码">
        </div>

        <div id="join-section" class="hidden">
            <button id="join-btn" class="w-full bg-blue-500 hover:bg-blue-600 text-white py-3 px-4 rounded-lg font-medium">
                <i class="fas fa-door-open mr-2"></i>加入房间
            </button>
        </div>

        <div id="error-message" class="mt-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded-lg hidden">
            <i class="fas fa-exclamation-triangle mr-2"></i>
            <span id="error-text"></span>
        </div>

        <div id="loading" class="text-center py-8 hidden">
            <i class="fas fa-spinner fa-spin text-2xl text-blue-500 mb-2"></i>
            <p class="text-gray-600">正在加载...</p>
        </div>
    </div>

    <script>
        class InviteHandler {
            constructor() {
                this.inviteCode = window.location.pathname.split('/').pop();
                this.currentUser = null;
                this.roomInfo = null;
                
                this.init();
            }

            async init() {
                await this.checkAuthStatus();
                await this.loadRoomInfo();
                this.bindEvents();
            }

            async checkAuthStatus() {
                try {
                    const response = await fetch('/api/auth/me');
                    if (response.ok) {
                        this.currentUser = await response.json();
                    }
                } catch (error) {
                    console.error('检查认证状态错误:', error);
                }
            }

            async loadRoomInfo() {
                const loading = document.getElementById('loading');
                loading.classList.remove('hidden');

                try {
                    // 这里我们需要一个API来获取邀请码对应的房间信息
                    // 暂时先显示登录选项
                    this.showLoginOptions();
                } catch (error) {
                    console.error('加载房间信息错误:', error);
                    this.showError('无效的邀请链接或链接已过期');
                } finally {
                    loading.classList.add('hidden');
                }
            }

            showLoginOptions() {
                if (this.currentUser && !this.currentUser.is_anonymous) {
                    // 用户已登录，显示加入按钮
                    this.showJoinSection();
                } else {
                    // 用户未登录，显示登录选项
                    document.getElementById('login-section').classList.remove('hidden');
                }
            }

            showJoinSection() {
                document.getElementById('join-section').classList.remove('hidden');
                // 如果房间需要密码，显示密码输入框
                if (this.roomInfo && this.roomInfo.room_type === 'password') {
                    document.getElementById('password-section').classList.remove('hidden');
                }
            }

            showError(message) {
                document.getElementById('error-text').textContent = message;
                document.getElementById('error-message').classList.remove('hidden');
            }

            bindEvents() {
                document.getElementById('login-btn').addEventListener('click', () => {
                    window.location.href = '/login?redirect=' + encodeURIComponent(window.location.pathname);
                });

                document.getElementById('register-btn').addEventListener('click', () => {
                    window.location.href = '/register?redirect=' + encodeURIComponent(window.location.pathname);
                });

                document.getElementById('anonymous-btn').addEventListener('click', () => {
                    this.joinAsAnonymous();
                });

                document.getElementById('join-btn').addEventListener('click', () => {
                    this.joinRoom();
                });
            }

            async joinAsAnonymous() {
                try {
                    const response = await fetch('/api/auth/anonymous', {
                        method: 'POST'
                    });

                    if (response.ok) {
                        const data = await response.json();
                        this.currentUser = data.user;
                        this.showJoinSection();
                        document.getElementById('login-section').classList.add('hidden');
                    } else {
                        this.showError('匿名登录失败');
                    }
                } catch (error) {
                    console.error('匿名登录错误:', error);
                    this.showError('匿名登录失败，请重试');
                }
            }

            async joinRoom() {
                const password = document.getElementById('room-password').value;
                const joinBtn = document.getElementById('join-btn');
                
                joinBtn.disabled = true;
                joinBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>加入中...';

                try {
                    const response = await fetch(`/api/rooms/invite/${this.inviteCode}/join`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({ password: password || null })
                    });

                    if (response.ok) {
                        const data = await response.json();
                        // 加入成功，跳转到主页面
                        window.location.href = '/';
                    } else {
                        const error = await response.json();
                        this.showError(error.detail || '加入房间失败');
                    }
                } catch (error) {
                    console.error('加入房间错误:', error);
                    this.showError('加入房间失败，请重试');
                } finally {
                    joinBtn.disabled = false;
                    joinBtn.innerHTML = '<i class="fas fa-door-open mr-2"></i>加入房间';
                }
            }
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', () => {
            new InviteHandler();
        });
    </script>
</body>
</html>
