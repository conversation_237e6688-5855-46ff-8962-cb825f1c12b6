"""
认证服务
"""
import uuid
import random
import string
from datetime import datetime, timedelta, timezone
from typing import Optional, Union
from jose import JWTError, jwt
from passlib.context import CryptContext
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from app.core.config import settings
from app.core.database import get_db
from app.models.user import User, UserRole, UserStatus


# 密码加密上下文
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")


def generate_anonymous_nickname() -> str:
    """生成随机匿名昵称，格式：匿名XXXXX（随机数字+随机大小写英文）"""
    # 生成3位随机数字
    numbers = ''.join(random.choices(string.digits, k=3))

    # 生成2位随机大小写英文字母
    letters = ''.join(random.choices(string.ascii_letters, k=2))

    # 组合成昵称
    return f"匿名{numbers}{letters}"


def verify_password(plain_password: str, hashed_password: str) -> bool:
    """验证密码"""
    return pwd_context.verify(plain_password, hashed_password)


def get_password_hash(password: str) -> str:
    """获取密码哈希"""
    return pwd_context.hash(password)


def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    """创建访问令牌"""
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.now(timezone.utc) + expires_delta
    else:
        expire = datetime.now(timezone.utc) + timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)

    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, settings.JWT_SECRET_KEY, algorithm=settings.JWT_ALGORITHM)
    return encoded_jwt


def verify_token(token: str) -> Optional[dict]:
    """验证令牌"""
    try:
        payload = jwt.decode(token, settings.JWT_SECRET_KEY, algorithms=[settings.JWT_ALGORITHM])
        return payload
    except JWTError:
        return None


async def get_user_by_id(db: AsyncSession, user_id: str) -> Optional[User]:
    """根据ID获取用户"""
    result = await db.execute(select(User).where(User.id == user_id))
    return result.scalar_one_or_none()


async def get_user_by_username(db: AsyncSession, username: str) -> Optional[User]:
    """根据用户名获取用户"""
    result = await db.execute(select(User).where(User.username == username))
    return result.scalar_one_or_none()


async def get_user_by_email(db: AsyncSession, email: str) -> Optional[User]:
    """根据邮箱获取用户"""
    result = await db.execute(select(User).where(User.email == email))
    return result.scalar_one_or_none()


async def get_user_by_anonymous_id(db: AsyncSession, anonymous_id: str) -> Optional[User]:
    """根据匿名ID获取用户"""
    result = await db.execute(select(User).where(User.anonymous_id == anonymous_id))
    return result.scalar_one_or_none()


async def create_anonymous_user(db: AsyncSession, anonymous_id: str = None) -> User:
    """创建匿名用户"""
    if not anonymous_id:
        anonymous_id = str(uuid.uuid4())

    user = User(
        anonymous_id=anonymous_id,
        is_anonymous=True,
        display_name=generate_anonymous_nickname(),
        role=UserRole.USER,
        status=UserStatus.ACTIVE,
        is_active=True
    )

    db.add(user)
    await db.commit()
    await db.refresh(user)
    return user


async def authenticate_user(db: AsyncSession, username: str, password: str) -> Optional[User]:
    """认证用户"""
    user = await get_user_by_username(db, username)
    if not user:
        user = await get_user_by_email(db, username)  # 也支持邮箱登录

    if not user or not user.hashed_password:
        return None

    if not verify_password(password, user.hashed_password):
        return None

    # 更新最后登录时间
    user.last_login_at = datetime.now(timezone.utc)
    await db.commit()

    return user


async def register_user(db: AsyncSession, username: str, email: str, password: str,
                       anonymous_id: str = None) -> Union[User, str]:
    """注册用户"""
    # 检查用户名是否已存在
    existing_user = await get_user_by_username(db, username)
    if existing_user:
        return "用户名已存在"

    # 检查邮箱是否已存在（只有当email不为None时才检查）
    if email:
        existing_email = await get_user_by_email(db, email)
        if existing_email:
            return "邮箱已存在"

    # 如果提供了匿名ID，尝试升级匿名用户
    if anonymous_id:
        anonymous_user = await get_user_by_anonymous_id(db, anonymous_id)
        if anonymous_user and anonymous_user.is_anonymous:
            # 升级匿名用户为注册用户
            anonymous_user.username = username
            anonymous_user.email = email
            anonymous_user.hashed_password = get_password_hash(password)
            anonymous_user.is_anonymous = False
            anonymous_user.last_login_at = datetime.now(timezone.utc)

            await db.commit()
            await db.refresh(anonymous_user)
            return anonymous_user

    # 创建新用户
    user = User(
        username=username,
        email=email,
        hashed_password=get_password_hash(password),
        anonymous_id=anonymous_id or str(uuid.uuid4()),
        is_anonymous=False,
        role=UserRole.USER,
        status=UserStatus.ACTIVE,
        is_active=True,
        last_login_at=datetime.now(timezone.utc)
    )

    db.add(user)
    await db.commit()
    await db.refresh(user)
    return user


async def create_admin_user():
    """创建管理员用户"""
    async for db in get_db():
        admin = await get_user_by_username(db, settings.ADMIN_USERNAME)
        if not admin:
            admin = User(
                username=settings.ADMIN_USERNAME,
                hashed_password=get_password_hash(settings.ADMIN_PASSWORD),
                anonymous_id=str(uuid.uuid4()),
                is_anonymous=False,
                role=UserRole.ADMIN,
                status=UserStatus.ACTIVE,
                is_active=True,
                can_create_room=True
            )
            db.add(admin)
            await db.commit()
            print(f"管理员用户已创建: {settings.ADMIN_USERNAME}")
        break