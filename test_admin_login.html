<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理员登录流程测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            border: 1px solid #ddd;
            padding: 20px;
            margin: 20px 0;
            border-radius: 5px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .info {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <h1>管理员登录流程测试</h1>
    
    <div class="test-section">
        <h2>测试步骤</h2>
        <ol>
            <li>清除现有token</li>
            <li>尝试访问管理员页面（应该重定向到登录页面）</li>
            <li>在登录页面输入管理员凭据</li>
            <li>登录成功后应该重定向回管理员页面</li>
            <li>测试登出功能</li>
        </ol>
    </div>

    <div class="test-section">
        <h2>当前状态</h2>
        <div id="status" class="info">
            <p>Token: <span id="token-status">检查中...</span></p>
            <p>管理员权限: <span id="admin-status">检查中...</span></p>
        </div>
        <button onclick="checkStatus()">刷新状态</button>
    </div>

    <div class="test-section">
        <h2>测试操作</h2>
        <button onclick="clearToken()">清除Token</button>
        <button onclick="testAdminAccess()">测试管理员页面访问</button>
        <button onclick="openLogin()">打开登录页面</button>
        <button onclick="testLogin()">测试登录</button>
    </div>

    <div class="test-section">
        <h2>测试日志</h2>
        <div id="log"></div>
        <button onclick="clearLog()">清空日志</button>
    </div>

    <script>
        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = type;
            logEntry.innerHTML = `<strong>[${timestamp}]</strong> ${message}`;
            logDiv.appendChild(logEntry);
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }

        async function checkStatus() {
            const token = localStorage.getItem('access_token');
            document.getElementById('token-status').textContent = token ? '存在' : '不存在';
            
            if (token) {
                try {
                    const response = await fetch('/api/admin/stats', {
                        headers: {
                            'Authorization': `Bearer ${token}`
                        }
                    });
                    
                    if (response.ok) {
                        document.getElementById('admin-status').textContent = '有权限';
                        log('管理员权限检查: 通过', 'success');
                    } else {
                        document.getElementById('admin-status').textContent = '无权限';
                        log(`管理员权限检查: 失败 (${response.status})`, 'error');
                    }
                } catch (error) {
                    document.getElementById('admin-status').textContent = '检查失败';
                    log(`管理员权限检查: 错误 - ${error.message}`, 'error');
                }
            } else {
                document.getElementById('admin-status').textContent = '无Token';
                log('管理员权限检查: 无Token', 'info');
            }
        }

        function clearToken() {
            localStorage.removeItem('access_token');
            log('Token已清除', 'info');
            checkStatus();
        }

        function testAdminAccess() {
            log('尝试访问管理员页面...', 'info');
            window.open('/admin', '_blank');
        }

        function openLogin() {
            log('打开登录页面...', 'info');
            window.open('/login', '_blank');
        }

        async function testLogin() {
            try {
                log('开始测试登录...', 'info');
                
                const response = await fetch('/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: 'admin',
                        password: 'admin123'
                    })
                });

                if (response.ok) {
                    const data = await response.json();
                    localStorage.setItem('access_token', data.access_token);
                    log('登录成功！Token已保存', 'success');
                    checkStatus();
                } else {
                    const error = await response.json();
                    log(`登录失败: ${error.detail}`, 'error');
                }
            } catch (error) {
                log(`登录错误: ${error.message}`, 'error');
            }
        }

        // 页面加载时检查状态
        window.onload = function() {
            checkStatus();
        };
    </script>
</body>
</html>
