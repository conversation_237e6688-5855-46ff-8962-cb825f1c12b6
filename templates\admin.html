<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理员面板 - 聊天平台</title>
    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>💬</text></svg>">
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .admin-card {
            transition: transform 0.2s, box-shadow 0.2s;
        }
        .admin-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .stat-card.green {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }
        .stat-card.orange {
            background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
        }
        .stat-card.purple {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
        }
    </style>
</head>
<body class="bg-gray-100">
    <!-- 导航栏 -->
    <nav class="bg-white shadow-sm border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <i class="fas fa-shield-alt text-2xl text-purple-600 mr-3"></i>
                    <h1 class="text-xl font-semibold text-gray-900">管理员面板</h1>
                </div>
                <div class="flex items-center space-x-4">
                    <span id="admin-name" class="text-sm text-gray-700">管理员</span>
                    <a href="/" class="text-sm text-blue-600 hover:text-blue-800">返回主页</a>
                    <button id="logout-btn" class="text-sm text-red-600 hover:text-red-800">退出登录</button>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <!-- 统计卡片 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div class="stat-card text-white p-6 rounded-lg shadow">
                <div class="flex items-center">
                    <div class="flex-1">
                        <p class="text-sm font-medium opacity-90">总用户数</p>
                        <p id="total-users" class="text-3xl font-bold">0</p>
                    </div>
                    <i class="fas fa-users text-2xl opacity-80"></i>
                </div>
            </div>
            <div class="stat-card green text-white p-6 rounded-lg shadow">
                <div class="flex items-center">
                    <div class="flex-1">
                        <p class="text-sm font-medium opacity-90">活跃用户</p>
                        <p id="active-users" class="text-3xl font-bold">0</p>
                    </div>
                    <i class="fas fa-user-check text-2xl opacity-80"></i>
                </div>
            </div>
            <div class="stat-card orange text-white p-6 rounded-lg shadow">
                <div class="flex items-center">
                    <div class="flex-1">
                        <p class="text-sm font-medium opacity-90">房间数量</p>
                        <p id="total-rooms" class="text-3xl font-bold">0</p>
                    </div>
                    <i class="fas fa-comments text-2xl opacity-80"></i>
                </div>
            </div>
            <div class="stat-card purple text-gray-800 p-6 rounded-lg shadow">
                <div class="flex items-center">
                    <div class="flex-1">
                        <p class="text-sm font-medium opacity-90">今日消息</p>
                        <p id="messages-today" class="text-3xl font-bold">0</p>
                    </div>
                    <i class="fas fa-envelope text-2xl opacity-80"></i>
                </div>
            </div>
        </div>

        <!-- 系统设置 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <h2 class="text-xl font-semibold text-gray-800 mb-4">系统设置</h2>
            <div class="space-y-4">
                <div class="flex items-center justify-between">
                    <div>
                        <label class="text-sm font-medium text-gray-700">调试模式</label>
                        <p class="text-xs text-gray-500">启用后在终端显示详细的运行日志</p>
                    </div>
                    <label class="relative inline-flex items-center cursor-pointer">
                        <input type="checkbox" id="debug-mode-toggle" class="sr-only peer">
                        <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                    </label>
                </div>
            </div>
        </div>

        <!-- 管理面板 -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- 用户管理 -->
            <div class="bg-white rounded-lg shadow admin-card">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">用户管理</h3>
                </div>
                <div class="p-6">
                    <div class="mb-4">
                        <input type="text" id="user-search" placeholder="搜索用户..." 
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                    <div id="users-list" class="space-y-3 max-h-96 overflow-y-auto">
                        <!-- 用户列表将在这里动态加载 -->
                    </div>
                    <div class="mt-4 flex justify-between items-center">
                        <button id="prev-users" class="px-3 py-1 text-sm bg-gray-200 rounded hover:bg-gray-300" disabled>上一页</button>
                        <span id="users-page-info" class="text-sm text-gray-600">第 1 页</span>
                        <button id="next-users" class="px-3 py-1 text-sm bg-gray-200 rounded hover:bg-gray-300">下一页</button>
                    </div>
                </div>
            </div>

            <!-- 房间管理 -->
            <div class="bg-white rounded-lg shadow admin-card">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">房间管理</h3>
                </div>
                <div class="p-6">
                    <div class="mb-4">
                        <input type="text" id="room-search" placeholder="搜索房间..." 
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                    <div id="rooms-list" class="space-y-3 max-h-96 overflow-y-auto">
                        <!-- 房间列表将在这里动态加载 -->
                    </div>
                    <div class="mt-4 flex justify-between items-center">
                        <button id="prev-rooms" class="px-3 py-1 text-sm bg-gray-200 rounded hover:bg-gray-300" disabled>上一页</button>
                        <span id="rooms-page-info" class="text-sm text-gray-600">第 1 页</span>
                        <button id="next-rooms" class="px-3 py-1 text-sm bg-gray-200 rounded hover:bg-gray-300">下一页</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 通知容器 -->
    <div id="notification-container" class="fixed top-4 right-4 z-50"></div>

    <script src="/static/js/admin.js"></script>
</body>
</html>
