"""
聊天平台主应用入口
"""
import os
import uvicorn
from fastapi import FastAPI, Request
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from contextlib import asynccontextmanager

from app.core.config import settings
from app.core.database import engine, Base
from app.routers import auth
# from app.services.websocket_manager import websocket_manager


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时创建数据库表
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)

    # 初始化管理员用户
    from app.services.auth_service import create_admin_user
    await create_admin_user()

    yield

    # 关闭时清理资源
    await engine.dispose()


# 创建FastAPI应用
app = FastAPI(
    title=settings.APP_NAME,
    version=settings.APP_VERSION,
    description="现代化的1V1聊天平台",
    lifespan=lifespan
)

# 添加中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 生产环境中应该限制具体域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.add_middleware(
    TrustedHostMiddleware,
    allowed_hosts=["*"]  # 生产环境中应该限制具体主机
)

# 静态文件和模板
app.mount("/static", StaticFiles(directory="static"), name="static")
app.mount("/uploads", StaticFiles(directory="uploads"), name="uploads")
templates = Jinja2Templates(directory="templates")

# 注册路由
app.include_router(auth.router, prefix="/api/auth", tags=["认证"])

# 导入并启用房间路由
from app.routers import rooms
app.include_router(rooms.router, prefix="/api/rooms", tags=["房间"])

# 导入并启用聊天路由
from app.routers import chat
app.include_router(chat.router, prefix="/api/rooms", tags=["聊天"])
# app.include_router(files.router, prefix="/api/files", tags=["文件"])
# app.include_router(surveys.router, prefix="/api/surveys", tags=["问卷"])

# 导入并启用管理员路由
from app.routers import admin
app.include_router(admin.router, prefix="/api/admin", tags=["管理"])

# WebSocket连接
# app.include_router(websocket_manager.router)


@app.get("/")
async def root(request: Request):
    """首页"""
    return templates.TemplateResponse("index.html", {"request": request})


@app.get("/login")
async def login_page(request: Request):
    """登录页面"""
    return templates.TemplateResponse("login.html", {"request": request})


@app.get("/admin")
async def admin_panel(request: Request):
    """管理员面板 - 需要认证"""
    return templates.TemplateResponse("admin.html", {"request": request})


@app.get("/health")
async def health_check():
    """健康检查"""
    return {"status": "healthy", "version": settings.APP_VERSION}


if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=settings.DEBUG,
        log_level="info"
    )