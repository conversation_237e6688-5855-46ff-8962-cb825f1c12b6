"""
问卷模型
"""
import uuid
from datetime import datetime, timezone
from sqlalchemy import Column, String, Boolean, DateTime, Text, Enum, ForeignKey, JSON
from sqlalchemy.orm import relationship
import enum

from app.core.database import Base


class QuestionType(enum.Enum):
    """问题类型枚举"""
    SINGLE_CHOICE = "single_choice"    # 单选
    MULTIPLE_CHOICE = "multiple_choice"  # 多选
    TEXT = "text"                      # 文本
    RATING = "rating"                  # 评分


class SurveyStatus(enum.Enum):
    """问卷状态枚举"""
    DRAFT = "draft"        # 草稿
    ACTIVE = "active"      # 活跃
    INACTIVE = "inactive"  # 非活跃
    ARCHIVED = "archived"  # 已归档


class Survey(Base):
    """问卷模型"""
    __tablename__ = "surveys"

    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    title = Column(String(200), nullable=False)
    description = Column(Text, nullable=True)

    # 创建者
    creator_id = Column(String(36), ForeignKey("users.id"), nullable=False)

    # 问卷配置
    questions = Column(JSON, nullable=False)  # 问题列表
    is_anonymous = Column(Boolean, default=False)  # 是否匿名
    allow_multiple_responses = Column(Boolean, default=True)  # 是否允许多次回答

    # 状态
    status = Column(Enum(SurveyStatus), default=SurveyStatus.ACTIVE)
    is_active = Column(Boolean, default=True)

    # 时间戳
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    updated_at = Column(DateTime, default=lambda: datetime.now(timezone.utc),
                       onupdate=lambda: datetime.now(timezone.utc))

    # 关系
    creator = relationship("User")
    responses = relationship("SurveyResponse", back_populates="survey", cascade="all, delete-orphan")
    sent_instances = relationship("SurveySentInstance", back_populates="survey", cascade="all, delete-orphan")

    def __repr__(self):
        return f"<Survey {self.title}>"

    @property
    def response_count(self):
        """获取回答数量"""
        return len(self.responses)

    def to_dict(self):
        """转换为字典格式"""
        return {
            "id": str(self.id),
            "title": self.title,
            "description": self.description,
            "questions": self.questions,
            "is_anonymous": self.is_anonymous,
            "allow_multiple_responses": self.allow_multiple_responses,
            "status": self.status.value,
            "response_count": self.response_count,
            "created_at": self.created_at.isoformat(),
        }


class SurveySentInstance(Base):
    """问卷发送实例模型（每次发送都有唯一UUID）"""
    __tablename__ = "survey_sent_instances"

    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    survey_id = Column(String(36), ForeignKey("surveys.id"), nullable=False)
    room_id = Column(String(36), ForeignKey("rooms.id"), nullable=False)
    sender_id = Column(String(36), ForeignKey("users.id"), nullable=False)

    # 发送时间
    sent_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))

    # 关系
    survey = relationship("Survey", back_populates="sent_instances")
    room = relationship("Room")
    sender = relationship("User")
    responses = relationship("SurveyResponse", back_populates="sent_instance", cascade="all, delete-orphan")

    def __repr__(self):
        return f"<SurveySentInstance {self.id}>"

    @property
    def response_count(self):
        """获取此实例的回答数量"""
        return len(self.responses)


class SurveyResponse(Base):
    """问卷回答模型"""
    __tablename__ = "survey_responses"

    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    survey_id = Column(String(36), ForeignKey("surveys.id"), nullable=False)
    sent_instance_id = Column(String(36), ForeignKey("survey_sent_instances.id"), nullable=False)
    user_id = Column(String(36), ForeignKey("users.id"), nullable=False)

    # 回答内容
    answers = Column(JSON, nullable=False)  # 答案列表

    # 时间戳
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    updated_at = Column(DateTime, default=lambda: datetime.now(timezone.utc),
                       onupdate=lambda: datetime.now(timezone.utc))

    # 关系
    survey = relationship("Survey", back_populates="responses")
    sent_instance = relationship("SurveySentInstance", back_populates="responses")
    user = relationship("User", back_populates="survey_responses")

    def __repr__(self):
        return f"<SurveyResponse {self.id}>"

    def to_dict(self):
        """转换为字典格式"""
        return {
            "id": str(self.id),
            "survey_id": str(self.survey_id),
            "sent_instance_id": str(self.sent_instance_id),
            "user_id": str(self.user_id),
            "answers": self.answers,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat(),
        }