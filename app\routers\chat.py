"""
聊天消息路由
"""
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, desc
from pydantic import BaseModel
from datetime import datetime, timezone

from app.core.database import get_db
from app.models.user import User
from app.models.room import Room, RoomMember
from app.models.message import Message, MessageType, MessageStatus
from app.routers.auth import get_current_user

router = APIRouter()


# Pydantic模型
class MessageSendRequest(BaseModel):
    content: str
    type: str = "text"
    reply_to_id: Optional[str] = None


class MessageResponse(BaseModel):
    id: str
    room_id: str
    sender_id: str
    sender_username: str
    sender_display_name: Optional[str]
    message_type: str
    content: Optional[str]
    file_url: Optional[str]
    file_name: Optional[str]
    reply_to_id: Optional[str]
    status: str
    is_recalled: bool
    created_at: datetime
    updated_at: datetime


@router.post("/{room_id}/messages", response_model=MessageResponse)
async def send_message(
    room_id: str,
    message_data: MessageSendRequest,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """发送消息"""
    # 匿名用户也可以发送消息，不需要阻止

    # 检查房间是否存在
    result = await db.execute(select(Room).where(Room.id == room_id))
    room = result.scalar_one_or_none()

    if not room:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="房间不存在"
        )

    if not room.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="房间已关闭"
        )

    # 检查用户是否是房间成员
    member_result = await db.execute(
        select(RoomMember).where(
            and_(
                RoomMember.room_id == room_id,
                RoomMember.user_id == current_user.id,
                RoomMember.is_active == True
            )
        )
    )
    room_member = member_result.scalar_one_or_none()

    if not room_member:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="您不是该房间的成员"
        )

    # 验证消息类型
    try:
        msg_type = MessageType(message_data.type)
    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="无效的消息类型"
        )

    # 验证回复消息（如果有）
    reply_to_message = None
    if message_data.reply_to_id:
        reply_result = await db.execute(
            select(Message).where(
                and_(
                    Message.id == message_data.reply_to_id,
                    Message.room_id == room_id
                )
            )
        )
        reply_to_message = reply_result.scalar_one_or_none()
        if not reply_to_message:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="回复的消息不存在"
            )

    # 创建消息
    message = Message(
        room_id=room_id,
        sender_id=current_user.id,
        message_type=msg_type,
        content=message_data.content.strip() if message_data.content else None,
        reply_to_id=message_data.reply_to_id,
        status=MessageStatus.SENT
    )

    db.add(message)
    await db.commit()
    await db.refresh(message)

    # 返回消息响应
    return MessageResponse(
        id=str(message.id),
        room_id=str(message.room_id),
        sender_id=str(message.sender_id),
        sender_username=current_user.username,
        sender_display_name=current_user.display_name,
        message_type=message.message_type.value,
        content=message.content,
        file_url=message.file_url,
        file_name=message.file_name,
        reply_to_id=str(message.reply_to_id) if message.reply_to_id else None,
        status=message.status.value,
        is_recalled=message.is_recalled,
        created_at=message.created_at,
        updated_at=message.updated_at
    )


@router.get("/{room_id}/messages", response_model=List[MessageResponse])
async def get_messages(
    room_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
    page: int = Query(1, ge=1),
    size: int = Query(20, ge=1, le=100)
):
    """获取房间消息"""
    # 匿名用户也可以查看消息，不需要阻止

    # 检查房间是否存在
    result = await db.execute(select(Room).where(Room.id == room_id))
    room = result.scalar_one_or_none()

    if not room:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="房间不存在"
        )

    # 检查用户是否是房间成员
    member_result = await db.execute(
        select(RoomMember).where(
            and_(
                RoomMember.room_id == room_id,
                RoomMember.user_id == current_user.id,
                RoomMember.is_active == True
            )
        )
    )
    room_member = member_result.scalar_one_or_none()

    if not room_member:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="您不是该房间的成员"
        )

    # 获取消息列表
    offset = (page - 1) * size
    messages_result = await db.execute(
        select(Message, User).join(User, Message.sender_id == User.id)
        .where(Message.room_id == room_id)
        .order_by(desc(Message.created_at))
        .offset(offset)
        .limit(size)
    )

    messages_data = messages_result.all()

    # 转换为响应格式
    messages = []
    for message, sender in messages_data:
        messages.append(MessageResponse(
            id=str(message.id),
            room_id=str(message.room_id),
            sender_id=str(message.sender_id),
            sender_username=sender.username,
            sender_display_name=sender.display_name,
            message_type=message.message_type.value,
            content=message.content,
            file_url=message.file_url,
            file_name=message.file_name,
            reply_to_id=str(message.reply_to_id) if message.reply_to_id else None,
            status=message.status.value,
            is_recalled=message.is_recalled,
            created_at=message.created_at,
            updated_at=message.updated_at
        ))

    # 按时间正序返回（最新的在最后）
    return list(reversed(messages))