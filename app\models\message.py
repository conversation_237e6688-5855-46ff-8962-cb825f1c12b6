"""
消息模型
"""
import uuid
from datetime import datetime, timezone
from sqlalchemy import Column, String, Boolean, DateTime, Text, Enum, ForeignKey, JSON
from sqlalchemy.orm import relationship
import enum

from app.core.database import Base


class MessageType(enum.Enum):
    """消息类型枚举"""
    TEXT = "text"           # 文本消息
    IMAGE = "image"         # 图片消息
    AUDIO = "audio"         # 语音消息
    SYSTEM = "system"       # 系统消息
    SURVEY = "survey"       # 问卷消息
    PROFILE_REVEAL = "profile_reveal"  # 身份揭露消息


class MessageStatus(enum.Enum):
    """消息状态枚举"""
    SENT = "sent"
    DELIVERED = "delivered"
    READ = "read"
    RECALLED = "recalled"
    DELETED = "deleted"


class Message(Base):
    """消息模型"""
    __tablename__ = "messages"

    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    room_id = Column(String(36), ForeignKey("rooms.id"), nullable=False)
    sender_id = Column(String(36), ForeignKey("users.id"), nullable=False)

    # 消息内容
    message_type = Column(Enum(MessageType), default=MessageType.TEXT)
    content = Column(Text, nullable=True)  # 文本内容
    file_url = Column(String(500), nullable=True)  # 文件URL
    file_name = Column(String(255), nullable=True)  # 原始文件名
    file_size = Column(String(20), nullable=True)  # 文件大小
    thumbnail_url = Column(String(500), nullable=True)  # 缩略图URL

    # 引用回复
    reply_to_id = Column(String(36), ForeignKey("messages.id"), nullable=True)

    # 消息状态
    status = Column(Enum(MessageStatus), default=MessageStatus.SENT)
    is_recalled = Column(Boolean, default=False)
    recalled_at = Column(DateTime, nullable=True)
    recalled_by_id = Column(String(36), ForeignKey("users.id"), nullable=True)

    # 扩展数据（JSON格式，用于存储特殊消息的额外信息）
    extra_data = Column(JSON, nullable=True)

    # 时间戳
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    updated_at = Column(DateTime, default=lambda: datetime.now(timezone.utc),
                       onupdate=lambda: datetime.now(timezone.utc))

    # 关系
    room = relationship("Room", back_populates="messages")
    sender = relationship("User", back_populates="sent_messages", foreign_keys=[sender_id])
    recalled_by = relationship("User", foreign_keys=[recalled_by_id])
    reply_to = relationship("Message", remote_side=[id], backref="replies")

    def __repr__(self):
        return f"<Message {self.id} from {self.sender_id}>"

    @property
    def can_be_recalled(self):
        """检查消息是否可以撤回"""
        if self.is_recalled or self.status == MessageStatus.RECALLED:
            return False

        # 检查时间限制
        from app.core.config import settings
        time_limit = settings.MESSAGE_RECALL_TIME_LIMIT
        if time_limit > 0:
            elapsed = (datetime.now(timezone.utc) - self.created_at).total_seconds()
            return elapsed <= time_limit

        return True

    def recall(self, recalled_by_user):
        """撤回消息"""
        if not self.can_be_recalled:
            return False, "消息无法撤回"

        self.is_recalled = True
        self.status = MessageStatus.RECALLED
        self.recalled_at = datetime.now(timezone.utc)
        self.recalled_by_id = recalled_by_user.id
        return True, "消息已撤回"

    def to_dict(self):
        """转换为字典格式"""
        return {
            "id": str(self.id),
            "room_id": str(self.room_id),
            "sender_id": str(self.sender_id),
            "message_type": self.message_type.value,
            "content": self.content,
            "file_url": self.file_url,
            "file_name": self.file_name,
            "file_size": self.file_size,
            "thumbnail_url": self.thumbnail_url,
            "reply_to_id": str(self.reply_to_id) if self.reply_to_id else None,
            "status": self.status.value,
            "is_recalled": self.is_recalled,
            "recalled_at": self.recalled_at.isoformat() if self.recalled_at else None,
            "extra_data": self.extra_data,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat(),
        }