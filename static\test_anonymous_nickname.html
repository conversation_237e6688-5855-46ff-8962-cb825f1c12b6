<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>匿名用户昵称测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .user-info {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .nickname-examples {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin: 10px 0;
        }
        .nickname-example {
            background-color: #e9ecef;
            padding: 5px 10px;
            border-radius: 3px;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>匿名用户随机昵称测试</h1>
        
        <div class="test-section info">
            <h3>功能说明</h3>
            <p>匿名用户现在会显示随机昵称，格式为：<strong>匿名XXXXX</strong></p>
            <p>其中XXXXX是3位随机数字 + 2位随机大小写英文字母的组合</p>
            <div class="nickname-examples">
                <div class="nickname-example">匿名123Ab</div>
                <div class="nickname-example">匿名456Cd</div>
                <div class="nickname-example">匿名789Ef</div>
                <div class="nickname-example">匿名012Gh</div>
            </div>
        </div>

        <div class="test-section">
            <h3>当前用户信息</h3>
            <button onclick="getCurrentUser()">获取当前用户信息</button>
            <button onclick="clearCookies()">清除Cookie创建新匿名用户</button>
            <div id="current-user-info" class="user-info"></div>
        </div>

        <div class="test-section">
            <h3>创建多个匿名用户测试</h3>
            <button onclick="createMultipleAnonymousUsers()">创建5个匿名用户</button>
            <div id="multiple-users-info"></div>
        </div>

        <div class="test-section">
            <h3>测试日志</h3>
            <div id="test-log"></div>
        </div>
    </div>

    <script>
        function log(message, type = 'info') {
            const logDiv = document.getElementById('test-log');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `test-section ${type}`;
            logEntry.innerHTML = `<strong>[${timestamp}]</strong> ${message}`;
            logDiv.appendChild(logEntry);
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        async function getCurrentUser() {
            try {
                log('正在获取当前用户信息...', 'info');
                
                const response = await fetch('/api/auth/me');
                if (response.ok) {
                    const user = await response.json();
                    
                    const userInfoDiv = document.getElementById('current-user-info');
                    userInfoDiv.innerHTML = `
                        <h4>用户信息：</h4>
                        <p><strong>ID:</strong> ${user.id}</p>
                        <p><strong>用户名:</strong> ${user.username || '无'}</p>
                        <p><strong>显示名称:</strong> ${user.display_name || '无'}</p>
                        <p><strong>是否匿名:</strong> ${user.is_anonymous ? '是' : '否'}</p>
                        <p><strong>角色:</strong> ${user.role}</p>
                        <p><strong>可创建房间:</strong> ${user.can_create_room ? '是' : '否'}</p>
                    `;
                    
                    if (user.is_anonymous && user.display_name) {
                        if (user.display_name.startsWith('匿名')) {
                            log(`✅ 匿名用户昵称正确: ${user.display_name}`, 'success');
                        } else {
                            log(`❌ 匿名用户昵称格式错误: ${user.display_name}`, 'error');
                        }
                    } else if (user.is_anonymous) {
                        log(`❌ 匿名用户缺少display_name`, 'error');
                    } else {
                        log(`ℹ️ 当前用户不是匿名用户`, 'info');
                    }
                } else {
                    log(`❌ 获取用户信息失败: ${response.status}`, 'error');
                }
            } catch (error) {
                log(`❌ 请求错误: ${error.message}`, 'error');
            }
        }

        function clearCookies() {
            // 清除所有相关的cookie
            document.cookie = 'anonymous_id=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
            document.cookie = 'access_token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
            localStorage.removeItem('access_token');
            
            log('🧹 已清除所有认证信息', 'info');
            
            // 等待一下然后获取新用户
            setTimeout(getCurrentUser, 500);
        }

        async function createMultipleAnonymousUsers() {
            const multipleUsersDiv = document.getElementById('multiple-users-info');
            multipleUsersDiv.innerHTML = '<h4>创建的匿名用户：</h4>';
            
            log('开始创建5个匿名用户...', 'info');
            
            for (let i = 1; i <= 5; i++) {
                try {
                    // 每次都用新的fetch请求（不带cookie）来创建新的匿名用户
                    const response = await fetch('/api/auth/me', {
                        credentials: 'omit' // 不发送cookie
                    });
                    
                    if (response.ok) {
                        const user = await response.json();
                        
                        const userDiv = document.createElement('div');
                        userDiv.className = 'nickname-example';
                        userDiv.textContent = `${i}. ${user.display_name || '无昵称'}`;
                        multipleUsersDiv.appendChild(userDiv);
                        
                        if (user.display_name && user.display_name.startsWith('匿名')) {
                            log(`✅ 用户${i}: ${user.display_name}`, 'success');
                        } else {
                            log(`❌ 用户${i}: 昵称格式错误 - ${user.display_name}`, 'error');
                        }
                    } else {
                        log(`❌ 创建用户${i}失败: ${response.status}`, 'error');
                    }
                } catch (error) {
                    log(`❌ 创建用户${i}错误: ${error.message}`, 'error');
                }
                
                // 稍微延迟一下避免请求过快
                await new Promise(resolve => setTimeout(resolve, 200));
            }
            
            log('✅ 完成创建多个匿名用户测试', 'success');
        }

        // 页面加载时自动获取当前用户信息
        window.onload = function() {
            log('🚀 页面加载完成，开始测试', 'info');
            getCurrentUser();
        };
    </script>
</body>
</html>
