"""
用户模型
"""
import uuid
from datetime import datetime
from sqlalchemy import Column, String, Boolean, DateTime, Text, Enum
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
import enum

from app.core.database import Base


class UserRole(enum.Enum):
    """用户角色枚举"""
    ADMIN = "admin"
    USER = "user"
    GUEST = "guest"


class UserStatus(enum.Enum):
    """用户状态枚举"""
    ACTIVE = "active"
    INACTIVE = "inactive"
    BANNED = "banned"


class User(Base):
    """用户模型"""
    __tablename__ = "users"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    username = Column(String(50), unique=True, nullable=True, index=True)
    email = Column(String(100), unique=True, nullable=True, index=True)
    hashed_password = Column(String(255), nullable=True)

    # 匿名用户标识
    anonymous_id = Column(String(36), unique=True, nullable=False, index=True)
    is_anonymous = Column(Boolean, default=True)

    # 用户信息
    display_name = Column(String(100), nullable=True)
    avatar_url = Column(String(255), nullable=True)
    bio = Column(Text, nullable=True)

    # 权限和状态
    role = Column(Enum(UserRole), default=UserRole.USER)
    status = Column(Enum(UserStatus), default=UserStatus.ACTIVE)
    is_active = Column(Boolean, default=True)

    # 权限设置
    can_create_room = Column(Boolean, default=False)  # 需要管理员开通

    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    last_login_at = Column(DateTime, nullable=True)
    last_seen_at = Column(DateTime, nullable=True)

    # 关系
    created_rooms = relationship("Room", back_populates="creator", foreign_keys="Room.creator_id")
    room_memberships = relationship("RoomMember", back_populates="user")
    sent_messages = relationship("Message", back_populates="sender", foreign_keys="Message.sender_id")
    user_profiles = relationship("UserProfile", back_populates="user")
    survey_responses = relationship("SurveyResponse", back_populates="user")

    def __repr__(self):
        return f"<User {self.username or self.anonymous_id}>"

    @property
    def display_identifier(self):
        """显示标识符"""
        if self.is_anonymous:
            return f"匿名用户_{self.anonymous_id[:8]}"
        return self.username or self.display_name or f"用户_{self.id}"

    def can_access_room(self, room):
        """检查用户是否可以访问房间"""
        if self.role == UserRole.ADMIN:
            return True

        # 检查是否是房间成员
        from app.models.room import RoomMember
        return any(
            membership.room_id == room.id
            for membership in self.room_memberships
        )