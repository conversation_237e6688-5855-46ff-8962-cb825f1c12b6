<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>表情和文件功能测试</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gray-100">
    <div class="container mx-auto p-4">
        <h1 class="text-3xl font-bold text-center mb-8">表情和文件功能测试页面</h1>
        
        <!-- 登录区域 -->
        <div id="login-section" class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4">登录测试</h2>
            <div class="flex gap-4">
                <input type="text" id="username" placeholder="用户名" class="border rounded px-3 py-2" value="admin">
                <input type="password" id="password" placeholder="密码" class="border rounded px-3 py-2" value="admin123">
                <button onclick="testLogin()" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">登录</button>
            </div>
            <div id="login-status" class="mt-2 text-sm"></div>
        </div>

        <!-- 房间创建区域 -->
        <div id="room-section" class="bg-white rounded-lg shadow-md p-6 mb-6 hidden">
            <h2 class="text-xl font-semibold mb-4">房间管理</h2>
            <div class="flex gap-4 mb-4">
                <input type="text" id="room-name" placeholder="房间名称" class="border rounded px-3 py-2" value="表情文件测试房间">
                <button onclick="createTestRoom()" class="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600">创建房间</button>
            </div>
            <div id="room-status" class="text-sm"></div>
        </div>

        <!-- 表情测试区域 -->
        <div id="emoji-section" class="bg-white rounded-lg shadow-md p-6 mb-6 hidden">
            <h2 class="text-xl font-semibold mb-4">表情功能测试</h2>
            
            <!-- 表情输入框 -->
            <div class="relative mb-4">
                <div class="flex items-center border rounded-lg p-2">
                    <input type="text" id="emoji-input" placeholder="在这里输入消息，点击表情按钮添加表情..." class="flex-1 outline-none">
                    <button id="emoji-btn-test" class="ml-2 p-2 text-gray-500 hover:text-gray-700">
                        <i class="fas fa-smile text-xl"></i>
                    </button>
                    <button onclick="sendEmojiMessage()" class="ml-2 bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">发送</button>
                </div>
                
                <!-- 表情选择器 -->
                <div id="emoji-picker-test" class="absolute bottom-16 left-4 bg-white border border-gray-300 rounded-lg shadow-lg p-4 hidden z-50" style="width: 300px;">
                    <div class="grid grid-cols-8 gap-2 max-h-48 overflow-y-auto">
                        <!-- 常用表情 -->
                        <button class="emoji-btn-test text-2xl hover:bg-gray-100 p-1 rounded" data-emoji="😀">😀</button>
                        <button class="emoji-btn-test text-2xl hover:bg-gray-100 p-1 rounded" data-emoji="😃">😃</button>
                        <button class="emoji-btn-test text-2xl hover:bg-gray-100 p-1 rounded" data-emoji="😄">😄</button>
                        <button class="emoji-btn-test text-2xl hover:bg-gray-100 p-1 rounded" data-emoji="😁">😁</button>
                        <button class="emoji-btn-test text-2xl hover:bg-gray-100 p-1 rounded" data-emoji="😆">😆</button>
                        <button class="emoji-btn-test text-2xl hover:bg-gray-100 p-1 rounded" data-emoji="😅">😅</button>
                        <button class="emoji-btn-test text-2xl hover:bg-gray-100 p-1 rounded" data-emoji="😂">😂</button>
                        <button class="emoji-btn-test text-2xl hover:bg-gray-100 p-1 rounded" data-emoji="🤣">🤣</button>
                        <button class="emoji-btn-test text-2xl hover:bg-gray-100 p-1 rounded" data-emoji="👍">👍</button>
                        <button class="emoji-btn-test text-2xl hover:bg-gray-100 p-1 rounded" data-emoji="👌">👌</button>
                        <button class="emoji-btn-test text-2xl hover:bg-gray-100 p-1 rounded" data-emoji="✌️">✌️</button>
                        <button class="emoji-btn-test text-2xl hover:bg-gray-100 p-1 rounded" data-emoji="🤞">🤞</button>
                        <button class="emoji-btn-test text-2xl hover:bg-gray-100 p-1 rounded" data-emoji="❤️">❤️</button>
                        <button class="emoji-btn-test text-2xl hover:bg-gray-100 p-1 rounded" data-emoji="💛">💛</button>
                        <button class="emoji-btn-test text-2xl hover:bg-gray-100 p-1 rounded" data-emoji="💚">💚</button>
                        <button class="emoji-btn-test text-2xl hover:bg-gray-100 p-1 rounded" data-emoji="💙">💙</button>
                    </div>
                </div>
            </div>
            
            <div id="emoji-status" class="text-sm"></div>
        </div>

        <!-- 文件上传测试区域 -->
        <div id="file-section" class="bg-white rounded-lg shadow-md p-6 mb-6 hidden">
            <h2 class="text-xl font-semibold mb-4">文件上传功能测试</h2>
            
            <div class="mb-4">
                <div class="flex items-center gap-4">
                    <input type="file" id="file-input-test" accept="image/*,audio/*" class="hidden">
                    <button onclick="document.getElementById('file-input-test').click()" class="bg-purple-500 text-white px-4 py-2 rounded hover:bg-purple-600">
                        <i class="fas fa-paperclip mr-2"></i>选择文件
                    </button>
                    <button onclick="uploadTestFile()" class="bg-orange-500 text-white px-4 py-2 rounded hover:bg-orange-600">上传文件</button>
                </div>
                <div id="file-info" class="mt-2 text-sm text-gray-600"></div>
            </div>
            
            <div id="file-status" class="text-sm"></div>
        </div>

        <!-- 消息显示区域 -->
        <div id="messages-section" class="bg-white rounded-lg shadow-md p-6 hidden">
            <h2 class="text-xl font-semibold mb-4">消息显示测试</h2>
            <div id="messages-container" class="border rounded-lg p-4 h-64 overflow-y-auto bg-gray-50">
                <!-- 消息将在这里显示 -->
            </div>
            <button onclick="loadMessages()" class="mt-4 bg-indigo-500 text-white px-4 py-2 rounded hover:bg-indigo-600">刷新消息</button>
        </div>
    </div>

    <script>
        let authToken = '';
        let currentRoomId = '';

        // 登录测试
        async function testLogin() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const statusDiv = document.getElementById('login-status');

            try {
                const response = await fetch('/api/auth/login', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ username, password })
                });

                if (response.ok) {
                    const data = await response.json();
                    authToken = data.access_token;
                    statusDiv.innerHTML = '<span class="text-green-600">✅ 登录成功</span>';
                    
                    // 显示其他测试区域
                    document.getElementById('room-section').classList.remove('hidden');
                } else {
                    const error = await response.json();
                    statusDiv.innerHTML = `<span class="text-red-600">❌ 登录失败: ${error.detail}</span>`;
                }
            } catch (error) {
                statusDiv.innerHTML = `<span class="text-red-600">❌ 登录错误: ${error.message}</span>`;
            }
        }

        // 创建测试房间
        async function createTestRoom() {
            const roomName = document.getElementById('room-name').value;
            const statusDiv = document.getElementById('room-status');

            try {
                const response = await fetch('/api/rooms/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    },
                    body: JSON.stringify({
                        name: roomName,
                        description: '用于测试表情和文件功能',
                        room_type: 'private',
                        max_members: 50
                    })
                });

                if (response.ok) {
                    const data = await response.json();
                    currentRoomId = data.id;
                    statusDiv.innerHTML = `<span class="text-green-600">✅ 房间创建成功，ID: ${currentRoomId}</span>`;
                    
                    // 显示测试区域
                    document.getElementById('emoji-section').classList.remove('hidden');
                    document.getElementById('file-section').classList.remove('hidden');
                    document.getElementById('messages-section').classList.remove('hidden');
                    
                    // 绑定表情事件
                    bindEmojiEvents();
                    bindFileEvents();
                } else {
                    const error = await response.json();
                    statusDiv.innerHTML = `<span class="text-red-600">❌ 房间创建失败: ${error.detail}</span>`;
                }
            } catch (error) {
                statusDiv.innerHTML = `<span class="text-red-600">❌ 房间创建错误: ${error.message}</span>`;
            }
        }

        // 绑定表情事件
        function bindEmojiEvents() {
            // 表情按钮点击事件
            document.getElementById('emoji-btn-test').addEventListener('click', () => {
                const picker = document.getElementById('emoji-picker-test');
                picker.classList.toggle('hidden');
            });

            // 表情选择事件
            document.querySelectorAll('.emoji-btn-test').forEach(btn => {
                btn.addEventListener('click', (e) => {
                    const emoji = e.target.getAttribute('data-emoji');
                    insertEmoji(emoji);
                });
            });

            // 点击外部关闭表情选择器
            document.addEventListener('click', (e) => {
                const picker = document.getElementById('emoji-picker-test');
                const emojiBtn = document.getElementById('emoji-btn-test');
                
                if (!picker.contains(e.target) && !emojiBtn.contains(e.target)) {
                    picker.classList.add('hidden');
                }
            });
        }

        // 插入表情
        function insertEmoji(emoji) {
            const input = document.getElementById('emoji-input');
            const cursorPosition = input.selectionStart;
            const currentValue = input.value;
            
            const newValue = currentValue.slice(0, cursorPosition) + emoji + currentValue.slice(cursorPosition);
            input.value = newValue;
            
            const newCursorPosition = cursorPosition + emoji.length;
            input.setSelectionRange(newCursorPosition, newCursorPosition);
            input.focus();
            
            document.getElementById('emoji-picker-test').classList.add('hidden');
        }

        // 发送表情消息
        async function sendEmojiMessage() {
            const content = document.getElementById('emoji-input').value;
            const statusDiv = document.getElementById('emoji-status');

            if (!content.trim()) {
                statusDiv.innerHTML = '<span class="text-yellow-600">⚠️ 请输入消息内容</span>';
                return;
            }

            try {
                const response = await fetch(`/api/rooms/${currentRoomId}/messages`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    },
                    body: JSON.stringify({ content, type: 'text' })
                });

                if (response.ok) {
                    statusDiv.innerHTML = '<span class="text-green-600">✅ 表情消息发送成功</span>';
                    document.getElementById('emoji-input').value = '';
                    loadMessages(); // 刷新消息列表
                } else {
                    const error = await response.json();
                    statusDiv.innerHTML = `<span class="text-red-600">❌ 消息发送失败: ${error.detail}</span>`;
                }
            } catch (error) {
                statusDiv.innerHTML = `<span class="text-red-600">❌ 消息发送错误: ${error.message}</span>`;
            }
        }

        // 绑定文件事件
        function bindFileEvents() {
            document.getElementById('file-input-test').addEventListener('change', (e) => {
                const file = e.target.files[0];
                const infoDiv = document.getElementById('file-info');
                
                if (file) {
                    infoDiv.innerHTML = `选择的文件: ${file.name} (${(file.size / 1024).toFixed(2)} KB)`;
                } else {
                    infoDiv.innerHTML = '';
                }
            });
        }

        // 上传测试文件
        async function uploadTestFile() {
            const fileInput = document.getElementById('file-input-test');
            const file = fileInput.files[0];
            const statusDiv = document.getElementById('file-status');

            if (!file) {
                statusDiv.innerHTML = '<span class="text-yellow-600">⚠️ 请先选择文件</span>';
                return;
            }

            try {
                // 上传文件
                const formData = new FormData();
                formData.append('file', file);

                const uploadResponse = await fetch(`/api/rooms/${currentRoomId}/upload`, {
                    method: 'POST',
                    headers: { 'Authorization': `Bearer ${authToken}` },
                    body: formData
                });

                if (uploadResponse.ok) {
                    const uploadResult = await uploadResponse.json();
                    statusDiv.innerHTML = `<span class="text-green-600">✅ 文件上传成功: ${uploadResult.file_url}</span>`;

                    // 发送文件消息
                    const messageResponse = await fetch(`/api/rooms/${currentRoomId}/messages`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Authorization': `Bearer ${authToken}`
                        },
                        body: JSON.stringify({
                            content: uploadResult.file_url,
                            type: file.type.startsWith('image/') ? 'image' : 'audio',
                            file_name: uploadResult.file_name,
                            file_size: uploadResult.file_size
                        })
                    });

                    if (messageResponse.ok) {
                        statusDiv.innerHTML += '<br><span class="text-green-600">✅ 文件消息发送成功</span>';
                        loadMessages(); // 刷新消息列表
                    } else {
                        const error = await messageResponse.json();
                        statusDiv.innerHTML += `<br><span class="text-red-600">❌ 文件消息发送失败: ${error.detail}</span>`;
                    }
                } else {
                    const error = await uploadResponse.json();
                    statusDiv.innerHTML = `<span class="text-red-600">❌ 文件上传失败: ${error.detail}</span>`;
                }
            } catch (error) {
                statusDiv.innerHTML = `<span class="text-red-600">❌ 文件上传错误: ${error.message}</span>`;
            }
        }

        // 加载消息
        async function loadMessages() {
            if (!currentRoomId) return;

            try {
                const response = await fetch(`/api/rooms/${currentRoomId}/messages`, {
                    headers: { 'Authorization': `Bearer ${authToken}` }
                });

                if (response.ok) {
                    const messages = await response.json();
                    const container = document.getElementById('messages-container');
                    
                    container.innerHTML = messages.map(msg => {
                        let content = '';
                        if (msg.message_type === 'image' || msg.type === 'image') {
                            content = `<img src="${msg.content}" alt="图片" class="max-w-xs max-h-32 rounded cursor-pointer" onclick="window.open('${msg.content}', '_blank')">`;
                        } else if (msg.message_type === 'audio' || msg.type === 'audio') {
                            content = `<audio controls class="max-w-xs"><source src="${msg.content}" type="audio/mpeg">您的浏览器不支持音频播放。</audio>`;
                        } else {
                            content = msg.content;
                        }
                        
                        return `
                            <div class="mb-2 p-2 bg-white rounded border">
                                <div class="text-xs text-gray-500">${msg.sender_username || '匿名用户'} - ${new Date(msg.created_at).toLocaleString()}</div>
                                <div class="mt-1">${content}</div>
                            </div>
                        `;
                    }).join('');
                    
                    container.scrollTop = container.scrollHeight;
                }
            } catch (error) {
                console.error('加载消息错误:', error);
            }
        }
    </script>
</body>
</html>
