// 管理员面板应用
class AdminApp {
    constructor() {
        this.token = localStorage.getItem('access_token');
        this.currentUser = null;
        this.usersPage = 1;
        this.roomsPage = 1;
        this.pageSize = 10;
        
        this.init();
    }

    init() {
        // 直接初始化管理面板（认证检查已在HTML中完成）
        this.initAfterAuth();
    }

    async initAfterAuth() {
        // 绑定事件
        this.bindEvents();

        // 加载数据
        this.loadStats();
        this.loadUsers();
        this.loadRooms();
        this.loadDebugMode();
    }

    bindEvents() {
        // 搜索事件
        document.getElementById('user-search').addEventListener('input', 
            this.debounce(() => this.loadUsers(), 500));
        document.getElementById('room-search').addEventListener('input', 
            this.debounce(() => this.loadRooms(), 500));
        
        // 分页事件
        document.getElementById('prev-users').addEventListener('click', () => {
            if (this.usersPage > 1) {
                this.usersPage--;
                this.loadUsers();
            }
        });
        document.getElementById('next-users').addEventListener('click', () => {
            this.usersPage++;
            this.loadUsers();
        });
        
        document.getElementById('prev-rooms').addEventListener('click', () => {
            if (this.roomsPage > 1) {
                this.roomsPage--;
                this.loadRooms();
            }
        });
        document.getElementById('next-rooms').addEventListener('click', () => {
            this.roomsPage++;
            this.loadRooms();
        });
        
        // 调试模式切换
        document.getElementById('debug-mode-toggle').addEventListener('change', (e) => {
            this.toggleDebugMode(e.target.checked);
        });

        // 退出登录
        document.getElementById('logout-btn').addEventListener('click', () => {
            this.logout();
        });
    }

    async checkAdminAuth() {
        if (!this.token) {
            this.redirectToLogin();
            return;
        }

        try {
            const response = await fetch('/api/auth/me', {
                headers: {
                    'Authorization': `Bearer ${this.token}`
                }
            });

            if (response.ok) {
                const user = await response.json();
                if (user.role !== 'ADMIN') {
                    this.showNotification('需要管理员权限', 'error');
                    setTimeout(() => window.location.href = '/', 2000);
                    return;
                }
                this.currentUser = user;
                document.getElementById('admin-name').textContent = user.username || '管理员';

                // 认证成功，初始化管理面板
                await this.initAfterAuth();
            } else {
                this.redirectToLogin();
            }
        } catch (error) {
            console.error('认证检查失败:', error);
            this.redirectToLogin();
        }
    }

    redirectToLogin() {
        // 保存当前页面URL作为重定向目标
        const currentUrl = encodeURIComponent(window.location.pathname + window.location.search);
        window.location.href = `/login?redirect=${currentUrl}`;
    }

    async loadStats() {
        try {
            const response = await fetch('/api/admin/stats', {
                headers: {
                    'Authorization': `Bearer ${this.token}`
                }
            });

            if (response.ok) {
                const stats = await response.json();
                document.getElementById('total-users').textContent = stats.total_users;
                document.getElementById('active-users').textContent = stats.active_users;
                document.getElementById('total-rooms').textContent = stats.total_rooms;
                document.getElementById('messages-today').textContent = stats.messages_today;
            }
        } catch (error) {
            console.error('加载统计数据失败:', error);
        }
    }

    async loadUsers() {
        const search = document.getElementById('user-search').value;
        const params = new URLSearchParams({
            page: this.usersPage,
            size: this.pageSize
        });
        
        if (search) {
            params.append('search', search);
        }

        try {
            const response = await fetch(`/api/admin/users?${params}`, {
                headers: {
                    'Authorization': `Bearer ${this.token}`
                }
            });

            if (response.ok) {
                const users = await response.json();
                this.renderUsers(users);
                this.updateUsersPagination(users.length);
            }
        } catch (error) {
            console.error('加载用户列表失败:', error);
        }
    }

    renderUsers(users) {
        const container = document.getElementById('users-list');
        container.innerHTML = '';

        users.forEach(user => {
            const userElement = document.createElement('div');
            userElement.className = 'flex items-center justify-between p-3 bg-gray-50 rounded-lg';
            
            const statusColor = user.is_active ? 'text-green-600' : 'text-red-600';
            const roleColor = user.role === 'admin' ? 'text-purple-600' : 'text-blue-600';
            
            userElement.innerHTML = `
                <div class="flex-1">
                    <div class="flex items-center space-x-2">
                        <span class="font-medium">${user.username || '匿名用户'}</span>
                        <span class="text-xs px-2 py-1 rounded ${roleColor} bg-opacity-10">${user.role}</span>
                        <span class="text-xs px-2 py-1 rounded ${statusColor} bg-opacity-10">${user.status}</span>
                    </div>
                    <div class="text-sm text-gray-500">
                        创建时间: ${new Date(user.created_at).toLocaleString()}
                        ${user.can_create_room ? ' | 可创建房间' : ''}
                    </div>
                </div>
                <div class="flex space-x-2">
                    ${this.getUserActionButtons(user)}
                </div>
            `;
            
            container.appendChild(userElement);
        });
    }

    getUserActionButtons(user) {
        if (user.role === 'admin') {
            return '<span class="text-xs text-gray-500">管理员</span>';
        }

        const buttons = [];
        
        if (user.is_active) {
            buttons.push(`<button onclick="adminApp.manageUser('${user.id}', 'deactivate')" class="text-xs px-2 py-1 bg-yellow-500 text-white rounded hover:bg-yellow-600">停用</button>`);
        } else {
            buttons.push(`<button onclick="adminApp.manageUser('${user.id}', 'activate')" class="text-xs px-2 py-1 bg-green-500 text-white rounded hover:bg-green-600">激活</button>`);
        }
        
        if (user.status !== 'banned') {
            buttons.push(`<button onclick="adminApp.manageUser('${user.id}', 'ban')" class="text-xs px-2 py-1 bg-red-500 text-white rounded hover:bg-red-600">封禁</button>`);
        } else {
            buttons.push(`<button onclick="adminApp.manageUser('${user.id}', 'unban')" class="text-xs px-2 py-1 bg-blue-500 text-white rounded hover:bg-blue-600">解封</button>`);
        }
        
        if (!user.can_create_room) {
            buttons.push(`<button onclick="adminApp.manageUser('${user.id}', 'grant_room_creation')" class="text-xs px-2 py-1 bg-purple-500 text-white rounded hover:bg-purple-600">授权建房</button>`);
        } else {
            buttons.push(`<button onclick="adminApp.manageUser('${user.id}', 'revoke_room_creation')" class="text-xs px-2 py-1 bg-gray-500 text-white rounded hover:bg-gray-600">撤销建房</button>`);
        }
        
        return buttons.join('');
    }

    async manageUser(userId, action) {
        try {
            const response = await fetch('/api/admin/users/manage', {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${this.token}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ user_id: userId, action })
            });

            if (response.ok) {
                const result = await response.json();
                this.showNotification(result.message, 'success');
                this.loadUsers();
                this.loadStats();
            } else {
                const error = await response.json();
                this.showNotification(error.detail || '操作失败', 'error');
            }
        } catch (error) {
            console.error('用户管理操作失败:', error);
            this.showNotification('操作失败，请重试', 'error');
        }
    }

    async loadRooms() {
        const search = document.getElementById('room-search').value;
        const params = new URLSearchParams({
            page: this.roomsPage,
            size: this.pageSize
        });
        
        if (search) {
            params.append('search', search);
        }

        try {
            const response = await fetch(`/api/admin/rooms?${params}`, {
                headers: {
                    'Authorization': `Bearer ${this.token}`
                }
            });

            if (response.ok) {
                const rooms = await response.json();
                this.renderRooms(rooms);
                this.updateRoomsPagination(rooms.length);
            }
        } catch (error) {
            console.error('加载房间列表失败:', error);
        }
    }

    renderRooms(rooms) {
        const container = document.getElementById('rooms-list');
        container.innerHTML = '';

        rooms.forEach(room => {
            const roomElement = document.createElement('div');
            roomElement.className = 'flex items-center justify-between p-3 bg-gray-50 rounded-lg';
            
            const statusColor = room.is_active ? 'text-green-600' : 'text-red-600';
            
            roomElement.innerHTML = `
                <div class="flex-1">
                    <div class="flex items-center space-x-2">
                        <span class="font-medium">${room.name}</span>
                        <span class="text-xs px-2 py-1 rounded text-blue-600 bg-blue-100">${room.room_type}</span>
                        <span class="text-xs px-2 py-1 rounded ${statusColor} bg-opacity-10">${room.status}</span>
                    </div>
                    <div class="text-sm text-gray-500">
                        创建者: ${room.creator_username || '未知'} | 成员: ${room.member_count} | 
                        创建时间: ${new Date(room.created_at).toLocaleString()}
                    </div>
                </div>
                <div class="flex space-x-2">
                    ${this.getRoomActionButtons(room)}
                </div>
            `;
            
            container.appendChild(roomElement);
        });
    }

    getRoomActionButtons(room) {
        const buttons = [];
        
        if (room.is_active) {
            buttons.push(`<button onclick="adminApp.manageRoom('${room.id}', 'deactivate')" class="text-xs px-2 py-1 bg-yellow-500 text-white rounded hover:bg-yellow-600">停用</button>`);
        } else {
            buttons.push(`<button onclick="adminApp.manageRoom('${room.id}', 'activate')" class="text-xs px-2 py-1 bg-green-500 text-white rounded hover:bg-green-600">激活</button>`);
        }
        
        buttons.push(`<button onclick="adminApp.manageRoom('${room.id}', 'delete')" class="text-xs px-2 py-1 bg-red-500 text-white rounded hover:bg-red-600">删除</button>`);
        
        return buttons.join('');
    }

    async manageRoom(roomId, action) {
        try {
            const response = await fetch('/api/admin/rooms/manage', {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${this.token}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ room_id: roomId, action })
            });

            if (response.ok) {
                const result = await response.json();
                this.showNotification(result.message, 'success');
                this.loadRooms();
                this.loadStats();
            } else {
                const error = await response.json();
                this.showNotification(error.detail || '操作失败', 'error');
            }
        } catch (error) {
            console.error('房间管理操作失败:', error);
            this.showNotification('操作失败，请重试', 'error');
        }
    }

    updateUsersPagination(count) {
        document.getElementById('users-page-info').textContent = `第 ${this.usersPage} 页`;
        document.getElementById('prev-users').disabled = this.usersPage <= 1;
        document.getElementById('next-users').disabled = count < this.pageSize;
    }

    updateRoomsPagination(count) {
        document.getElementById('rooms-page-info').textContent = `第 ${this.roomsPage} 页`;
        document.getElementById('prev-rooms').disabled = this.roomsPage <= 1;
        document.getElementById('next-rooms').disabled = count < this.pageSize;
    }

    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `px-4 py-2 rounded-md text-white mb-2 ${
            type === 'success' ? 'bg-green-500' :
            type === 'error' ? 'bg-red-500' :
            type === 'warning' ? 'bg-yellow-500' : 'bg-blue-500'
        }`;
        notification.textContent = message;
        
        const container = document.getElementById('notification-container');
        container.appendChild(notification);
        
        setTimeout(() => {
            notification.remove();
        }, 3000);
    }

    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    async loadDebugMode() {
        try {
            const response = await fetch('/api/admin/debug-mode', {
                headers: this.getAuthHeaders()
            });

            if (response.ok) {
                const data = await response.json();
                document.getElementById('debug-mode-toggle').checked = data.debug_mode;
            }
        } catch (error) {
            console.error('加载调试模式状态失败:', error);
        }
    }

    async toggleDebugMode(enabled) {
        try {
            const response = await fetch('/api/admin/debug-mode', {
                method: 'POST',
                headers: {
                    ...this.getAuthHeaders(),
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ debug_mode: enabled })
            });

            if (response.ok) {
                this.showNotification(
                    enabled ? '调试模式已启用' : '调试模式已关闭',
                    'success'
                );
            } else {
                throw new Error('设置调试模式失败');
            }
        } catch (error) {
            console.error('切换调试模式失败:', error);
            this.showNotification('设置调试模式失败', 'error');
            // 恢复开关状态
            document.getElementById('debug-mode-toggle').checked = !enabled;
        }
    }

    async logout() {
        try {
            // 调用后端登出API
            const response = await fetch('/api/auth/logout', {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${this.token}`,
                    'Content-Type': 'application/json'
                }
            });

            // 清除前端状态
            this.token = null;
            localStorage.removeItem('access_token');

            // 清除所有可能的cookie
            this.clearAllCookies();

            if (response.ok) {
                this.showNotification('已成功登出', 'success');
            } else {
                console.warn('后端登出API调用失败，但前端状态已清理');
            }

            // 延迟跳转，让用户看到提示信息
            setTimeout(() => {
                window.location.href = '/';
            }, 1000);

        } catch (error) {
            console.error('登出过程中发生错误:', error);
            // 即使出错也要清理前端状态
            this.token = null;
            localStorage.removeItem('access_token');
            this.clearAllCookies();

            // 直接跳转
            window.location.href = '/';
        }
    }

    clearAllCookies() {
        // 获取所有可能的cookie路径和域名组合
        const paths = ['/', '/api', '/api/auth'];
        const domains = [window.location.hostname, '.' + window.location.hostname, 'localhost', '.localhost'];

        // 清除anonymous_id cookie的所有可能组合
        paths.forEach(path => {
            // 不指定域名
            document.cookie = `anonymous_id=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=${path};`;
            document.cookie = `anonymous_id=; max-age=0; path=${path};`;

            // 指定各种域名
            domains.forEach(domain => {
                document.cookie = `anonymous_id=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=${path}; domain=${domain};`;
                document.cookie = `anonymous_id=; max-age=0; path=${path}; domain=${domain};`;
            });
        });
    }
}

// 初始化应用
const adminApp = new AdminApp();
